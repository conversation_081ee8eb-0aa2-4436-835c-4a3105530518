# RL-CT User Guide

## Overview

RL-CT (Reinforcement Learning for Cryptocurrency Trading) is a comprehensive framework for training and evaluating reinforcement learning agents on cryptocurrency trading tasks. This guide will help you get started with the framework.

## Installation

### Prerequisites

- Python 3.8 or higher
- CUDA-compatible GPU (optional, for faster training)

### Install Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd rl_ct

# Install the package
pip install -e .

# For development
pip install -e ".[dev]"
```

## Quick Start

### 1. Run the Demo

The fastest way to get started is to run the quick start demo:

```bash
python examples/quick_start.py
```

This will:
- Create sample cryptocurrency data
- Process the data with technical indicators
- Train a small model
- Evaluate the model
- Run backtesting

### 2. Collect Real Data

To collect real cryptocurrency data:

```bash
# Collect 30 days of hourly data for BTC and ETH
rl-ct collect-data --symbols BTC/USDT,ETH/USDT --days 30 --timeframe 1h
```

### 3. Train a Model

Train a model using the default configuration:

```bash
rl-ct train --config configs/training/default.yaml --name my_experiment
```

### 4. Evaluate the Model

Evaluate a trained model:

```bash
rl-ct evaluate checkpoints/my_experiment/best_model.zip --episodes 20
```

### 5. Run Backtesting

Run backtesting on historical data:

```bash
rl-ct backtest checkpoints/my_experiment/best_model.zip --balance 10000
```

## Configuration

The framework uses YAML configuration files for easy customization:

### Training Configuration

```yaml
# configs/training/my_config.yaml
experiment_name: "my_trading_bot"
seed: 42

# Environment settings
env:
  initial_balance: 10000.0
  transaction_cost: 0.001
  leverage: 1.0

# Algorithm settings
algorithm:
  name: "PPO"
  learning_rate: 3e-4
  batch_size: 64
  n_epochs: 10

# Model settings
model:
  type: "transformer"
  d_model: 256
  num_heads: 8
  num_layers: 3

# Training settings
training:
  total_timesteps: 1000000
  eval_freq: 10000
  save_freq: 50000
```

### Environment Configuration

```yaml
# configs/environment/my_env.yaml
initial_balance: 10000.0
leverage: 1.0
transaction_cost: 0.001
lookback_window: 168  # 1 week of hourly data
episode_length: 168

# Data configuration
dataset_name: "my_dataset"
data_dir: "data/processed"

# Reward configuration
reward_type: "pnl"  # pnl, sharpe, sortino
reward_scaling: 1.0
```

## Data Management

### Data Collection

The framework supports multiple data sources:

```bash
# Binance (default)
rl-ct collect-data --exchange binance --symbols BTC/USDT --days 90

# Coinbase
rl-ct collect-data --exchange coinbase --symbols BTC-USD --days 90

# Custom timeframes
rl-ct collect-data --timeframe 15m --days 30  # 15-minute data
rl-ct collect-data --timeframe 1d --days 365  # Daily data
```

### Data Processing

Data is automatically processed with:
- Technical indicators (RSI, MACD, Bollinger Bands, etc.)
- Time-based features (hour, day of week, etc.)
- Lag features
- Normalization/scaling

You can customize feature engineering in the configuration:

```yaml
feature_engineering:
  create_technical_indicators: true
  create_time_features: true
  create_lag_features: true

lag_features:
  columns: ["close", "volume", "rsi_14"]
  periods: [1, 2, 3, 5]

selected_features: null  # Use all features, or specify a list
```

## Model Architecture

### Transformer Model

The default model is a Transformer-based architecture:

```yaml
model:
  type: "transformer"
  d_model: 256        # Model dimension
  num_heads: 8        # Attention heads
  num_layers: 3       # Encoder layers
  dropout: 0.1        # Dropout rate
  activation: "gelu"  # Activation function
```

### MLP Model

For simpler tasks, you can use an MLP:

```yaml
model:
  type: "mlp"
  hidden_layers: [256, 256, 128]
  dropout: 0.1
  use_layer_norm: true
```

## Training

### Basic Training

```bash
rl-ct train --config configs/training/default.yaml
```

### Advanced Training Options

```bash
# Resume from checkpoint
rl-ct train --config my_config.yaml --resume --checkpoint path/to/checkpoint.zip

# Hyperparameter tuning
rl-ct train --config my_config.yaml --tune --trials 20

# Custom experiment name
rl-ct train --config my_config.yaml --name "btc_trading_v2"
```

### Monitoring Training

Training progress is logged to:
- Console output
- TensorBoard logs (if enabled)
- Weights & Biases (if configured)

```bash
# View TensorBoard logs
tensorboard --logdir logs/tensorboard
```

## Evaluation and Backtesting

### Model Evaluation

```bash
# Basic evaluation
rl-ct evaluate model.zip

# Detailed evaluation
rl-ct evaluate model.zip --episodes 50 --output results/detailed_eval
```

### Backtesting

```bash
# Basic backtest
rl-ct backtest model.zip

# Custom parameters
rl-ct backtest model.zip --balance 50000 --output results/backtest_50k
```

### Performance Metrics

The framework calculates comprehensive trading metrics:

- **Returns**: Total return, annualized return
- **Risk**: Volatility, max drawdown, VaR
- **Risk-adjusted**: Sharpe ratio, Sortino ratio, Calmar ratio
- **Trading**: Win rate, profit factor, average trade duration

## Advanced Features

### Custom Environments

Create custom trading environments by extending `BaseTradingEnv`:

```python
from rl_ct.envs import BaseTradingEnv

class MyTradingEnv(BaseTradingEnv):
    def _get_observation(self):
        # Custom observation logic
        pass
    
    def _calculate_reward(self, action, prev_balance):
        # Custom reward logic
        pass
    
    def _execute_action(self, action):
        # Custom action execution
        pass
```

### Custom Models

Implement custom models by extending `BaseModel`:

```python
from rl_ct.models import BaseModel

class MyModel(BaseModel):
    def forward(self, x):
        # Custom forward pass
        pass
```

### Custom Rewards

Implement custom reward functions:

```python
def custom_reward(self, action, prev_balance):
    # Your custom reward logic
    balance_change = self.balance - prev_balance
    risk_penalty = self.max_drawdown * 0.1
    return balance_change - risk_penalty
```

## Troubleshooting

### Common Issues

1. **CUDA out of memory**: Reduce batch size or model size
2. **Data loading errors**: Check data paths and formats
3. **Training instability**: Adjust learning rate or use gradient clipping

### Debug Mode

Enable debug mode for detailed logging:

```bash
rl-ct train --config my_config.yaml --debug
```

### Performance Optimization

- Use GPU for training: Set `device: "cuda"` in config
- Increase batch size for better GPU utilization
- Use mixed precision training for faster training
- Enable Ray's local mode for debugging: `local_mode: true`

## Best Practices

### Data Quality

- Use sufficient historical data (at least 6 months)
- Ensure data quality (no gaps, outliers handled)
- Include multiple market conditions (bull, bear, sideways)

### Model Training

- Start with smaller models and scale up
- Use proper train/validation/test splits
- Monitor for overfitting
- Regular evaluation during training

### Risk Management

- Always use transaction costs in simulation
- Test on out-of-sample data
- Consider market impact and slippage
- Implement position sizing and risk limits

### Production Deployment

- Thoroughly backtest before live trading
- Start with paper trading
- Monitor model performance continuously
- Have fallback strategies ready

## Support

For questions and support:
- Check the documentation
- Review example configurations
- Run the test suite: `pytest`
- Check the logs for error details

## Next Steps

1. Experiment with different model architectures
2. Try various reward functions
3. Implement custom features
4. Test on different cryptocurrencies
5. Explore multi-asset trading strategies
