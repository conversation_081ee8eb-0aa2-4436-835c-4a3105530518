#!/usr/bin/env python3
"""
Test script for the new transformer implementation.
"""

import torch
import numpy as np
from rl_ct.models.transformer import TransformerConfig, NewTransformerModel

def test_transformer_config():
    """Test transformer configuration."""
    print("Testing TransformerConfig...")
    
    config = TransformerConfig(
        num_obs_in_history=168,
        d_obs=100,
        d_time=2,
        d_account=2,
        d_technical_indicators=96,
        d_obs_enc=256,
        num_heads=4,
        num_attn_blocks=3,
        output_size=4
    )
    
    print(f"Config created successfully:")
    print(f"  - d_history_flat: {config.d_history_flat}")
    print(f"  - d_obs_internal_enc: {config.d_obs_internal_enc}")
    print(f"  - d_obs_external_enc: {config.d_obs_external_enc}")
    print(f"  - d_obs_stem_enc: {config.d_obs_stem_enc}")
    
    return config

def test_transformer_model(config):
    """Test transformer model."""
    print("\nTesting NewTransformerModel...")
    
    # Create model
    model = NewTransformerModel(config)
    print(f"Model created with {model.get_num_parameters():,} parameters")
    
    # Create test input
    batch_size = 4
    device = next(model.parameters()).device
    input_tensor = torch.randn(batch_size, config.d_history_flat, device=device)
    print(f"Input shape: {input_tensor.shape}")
    print(f"Input device: {input_tensor.device}")
    
    # Forward pass
    with torch.no_grad():
        policy_logits, value = model(input_tensor)
    
    print(f"Policy logits shape: {policy_logits.shape}")
    print(f"Value shape: {value.shape}")
    
    # Check output shapes
    assert policy_logits.shape == (batch_size, config.output_size), f"Expected policy shape {(batch_size, config.output_size)}, got {policy_logits.shape}"
    assert value.shape == (batch_size,), f"Expected value shape {(batch_size,)}, got {value.shape}"
    
    print("✓ Forward pass successful!")
    return model

def test_gradient_flow(model, config):
    """Test gradient flow."""
    print("\nTesting gradient flow...")
    
    # Create test input and target
    batch_size = 2
    device = next(model.parameters()).device
    input_tensor = torch.randn(batch_size, config.d_history_flat, device=device)
    target_policy = torch.randint(0, config.output_size, (batch_size,), device=device)
    target_value = torch.randn(batch_size, device=device)
    
    # Forward pass
    policy_logits, value = model(input_tensor)
    
    # Compute losses
    policy_loss = torch.nn.functional.cross_entropy(policy_logits, target_policy)
    value_loss = torch.nn.functional.mse_loss(value, target_value)
    total_loss = policy_loss + value_loss
    
    # Backward pass
    total_loss.backward()
    
    # Check gradients
    has_gradients = False
    for name, param in model.named_parameters():
        if param.grad is not None:
            has_gradients = True
            break
    
    assert has_gradients, "No gradients found!"
    print("✓ Gradient flow successful!")

def test_components_individually(config):
    """Test individual components."""
    print("\nTesting individual components...")
    
    from rl_ct.models.transformer import InputSplit, TimeEncoding, AccountEncoding, TechnicalIndicatorEncoding
    
    batch_size = 2
    seq_len = config.num_obs_in_history
    
    # Test InputSplit
    input_split = InputSplit(config)
    flat_input = torch.randn(batch_size, config.d_history_flat)
    time_data, account_data, technical_data = input_split(flat_input)
    
    print(f"InputSplit output shapes:")
    print(f"  - Time: {time_data.shape}")
    print(f"  - Account: {account_data.shape}")
    print(f"  - Technical: {technical_data.shape}")
    
    # Test TimeEncoding
    time_encoding = TimeEncoding(config.num_obs_in_history, config.d_time_enc)
    time_enc = time_encoding(time_data)
    print(f"TimeEncoding output shape: {time_enc.shape}")
    
    # Test AccountEncoding
    account_encoding = AccountEncoding(config.d_account_enc)
    account_enc = account_encoding(account_data)
    print(f"AccountEncoding output shape: {account_enc.shape}")
    
    # Test TechnicalIndicatorEncoding
    technical_encoding = TechnicalIndicatorEncoding(config.d_technical_indicators, config.d_technical_enc)
    technical_enc = technical_encoding(technical_data)
    print(f"TechnicalIndicatorEncoding output shape: {technical_enc.shape}")
    
    print("✓ All components working correctly!")

def main():
    """Main test function."""
    print("=" * 50)
    print("Testing New Transformer Implementation")
    print("=" * 50)
    
    try:
        # Test configuration
        config = test_transformer_config()
        
        # Test model
        model = test_transformer_model(config)
        
        # Test gradient flow
        test_gradient_flow(model, config)
        
        # Test individual components
        test_components_individually(config)
        
        print("\n" + "=" * 50)
        print("✓ All tests passed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
