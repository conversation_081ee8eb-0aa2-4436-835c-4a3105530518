#!/usr/bin/env python3
"""
Analyze the structure of feature_data to understand time feature positions.
"""

import numpy as np
import pandas as pd
from rl_ct.utils.preprocessing.data_processor import DataProcessor
from rl_ct.utils.preprocessing.feature_engineer import FeatureEngineer

def analyze_feature_structure():
    """Analyze the structure of features created by FeatureEngineer."""
    print("Analyzing feature structure...")
    
    # Create sample data with datetime index
    dates = pd.date_range('2023-01-01', periods=100, freq='1H')
    sample_data = pd.DataFrame({
        'open': np.random.randn(100) + 100,
        'high': np.random.randn(100) + 102,
        'low': np.random.randn(100) + 98,
        'close': np.random.randn(100) + 100,
        'volume': np.random.randn(100) * 1000 + 10000
    }, index=dates)
    
    print(f"Sample data shape: {sample_data.shape}")
    print(f"Sample data columns: {list(sample_data.columns)}")
    print(f"Date range: {sample_data.index[0]} to {sample_data.index[-1]}")
    
    # Create feature engineer
    feature_engineer = FeatureEngineer()
    
    # Create technical indicators
    df_with_tech = feature_engineer.create_technical_indicators(sample_data)
    print(f"\nAfter technical indicators: {df_with_tech.shape}")
    
    # Create time features
    df_with_time = feature_engineer.create_time_features(df_with_tech)
    print(f"After time features: {df_with_time.shape}")
    
    # Find time-related columns
    time_columns = []
    all_columns = list(df_with_time.columns)
    
    for col in all_columns:
        if any(time_word in col.lower() for time_word in ['hour', 'day', 'month', 'week', 'session', 'weekend']):
            time_columns.append(col)
    
    print(f"\nTime-related columns ({len(time_columns)}):")
    for i, col in enumerate(time_columns):
        print(f"  {i}: {col}")
    
    print(f"\nAll columns ({len(all_columns)}):")
    for i, col in enumerate(all_columns):
        print(f"  {i}: {col}")
    
    # Show sample values for time features
    print(f"\nSample time feature values (first 5 rows):")
    time_features_df = df_with_time[time_columns].head()
    print(time_features_df)
    
    # Identify which columns would be good for time encoding
    suggested_time_features = []
    for col in time_columns:
        if any(x in col for x in ['hour_sin', 'hour_cos', 'day_sin', 'day_cos']):
            suggested_time_features.append(col)
    
    print(f"\nSuggested time features for transformer (cyclical encoding): {suggested_time_features}")
    
    # Get indices of suggested features
    if suggested_time_features:
        indices = [all_columns.index(col) for col in suggested_time_features]
        print(f"Indices in feature array: {indices}")
    
    return all_columns, time_columns, suggested_time_features

def create_feature_mapping():
    """Create a mapping of feature types for the transformer."""
    all_columns, time_columns, suggested_time_features = analyze_feature_structure()
    
    # Define feature groups
    feature_mapping = {
        'time_features': suggested_time_features[:2] if len(suggested_time_features) >= 2 else suggested_time_features,
        'time_indices': [],
        'account_features': ['available_balance', 'unrealized_pnl'],  # These are added dynamically
        'technical_features': []
    }
    
    # Get indices for time features
    for feature in feature_mapping['time_features']:
        if feature in all_columns:
            feature_mapping['time_indices'].append(all_columns.index(feature))
    
    # All other features are technical features
    technical_features = []
    for i, col in enumerate(all_columns):
        if col not in feature_mapping['time_features']:
            technical_features.append(col)
    
    feature_mapping['technical_features'] = technical_features
    feature_mapping['technical_indices'] = list(range(len(all_columns)))
    
    # Remove time indices from technical indices
    for idx in feature_mapping['time_indices']:
        if idx in feature_mapping['technical_indices']:
            feature_mapping['technical_indices'].remove(idx)
    
    print(f"\nFeature mapping:")
    print(f"Time features: {feature_mapping['time_features']} (indices: {feature_mapping['time_indices']})")
    print(f"Technical features count: {len(feature_mapping['technical_features'])}")
    print(f"Technical indices: {feature_mapping['technical_indices'][:10]}...")  # Show first 10
    
    return feature_mapping

if __name__ == "__main__":
    feature_mapping = create_feature_mapping()
