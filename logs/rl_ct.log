2025-08-05 10:58:13,957 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/rl_ct.log
2025-08-05 10:58:13,961 - __main__ - INFO - test_api_stack.py:24 - Testing API stack configuration...
2025-08-05 10:58:13,963 - __main__ - INFO - test_api_stack.py:59 - Initializing PPOAgent with custom model...
2025-08-05 10:58:15,110 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:58:19,925 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 10:58:19,928 - __main__ - INFO - test_api_stack.py:68 - ✅ PPOAgent initialized successfully!
2025-08-05 10:58:19,931 - __main__ - INFO - test_api_stack.py:69 - ✅ Custom model configuration accepted!
2025-08-05 10:58:19,932 - __main__ - INFO - test_api_stack.py:70 - ✅ API stack configuration is working correctly!
2025-08-05 10:58:22,241 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 10:58:22,245 - __main__ - INFO - test_api_stack.py:74 - ✅ Test completed successfully!
