2025-08-05 10:31:21,350 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:31:21,351 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:31:21,353 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:31:26,261 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:31:26,271 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: AlgorithmConfig.training() got an unexpected keyword argument 'sgd_minibatch_size'
2025-08-05 10:31:26,273 - __main__ - ERROR - quick_start.py:193 - Training failed: AlgorithmConfig.training() got an unexpected keyword argument 'sgd_minibatch_size'
2025-08-05 10:31:26,275 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:31:26,276 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:31:26,277 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:31:26,279 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:31:26,280 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:31:26,281 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:31:26,282 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:31:26,284 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:31:26,285 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:31:26,286 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:31:26,287 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:31:26,288 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:31:26,290 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:31:26,291 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:31:26,292 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 10:34:03,437 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:34:03,438 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:34:03,440 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:34:07,358 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:34:07,365 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: `rollouts` has been deprecated. Use `AlgorithmConfig.env_runners(..)` instead.
2025-08-05 10:34:07,366 - __main__ - ERROR - quick_start.py:193 - Training failed: `rollouts` has been deprecated. Use `AlgorithmConfig.env_runners(..)` instead.
2025-08-05 10:34:07,368 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:34:07,369 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:34:07,371 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:34:07,372 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:34:07,373 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:34:07,374 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:34:07,376 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:34:07,377 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:34:07,378 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:34:07,379 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:34:07,380 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:34:07,381 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:34:07,383 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:34:07,384 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:34:07,385 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 10:42:30,263 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:42:30,265 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:42:30,266 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:42:34,716 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:42:34,726 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 10:42:34,729 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: `evaluation_parallel_to_training` can only be done if `evaluation_num_env_runners` > 0! Try setting `config.evaluation_parallel_to_training` to False.
To suppress all validation errors, set `config.experimental(_validate_config=False)` at your own risk.
2025-08-05 10:42:36,386 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:333 - PPOAgent closed
2025-08-05 10:42:36,390 - __main__ - ERROR - quick_start.py:193 - Training failed: `evaluation_parallel_to_training` can only be done if `evaluation_num_env_runners` > 0! Try setting `config.evaluation_parallel_to_training` to False.
To suppress all validation errors, set `config.experimental(_validate_config=False)` at your own risk.
2025-08-05 10:42:36,395 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:42:36,398 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:42:36,401 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:42:36,404 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:42:36,406 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:42:36,409 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:42:36,412 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:42:36,415 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:42:36,417 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:42:36,420 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:42:36,423 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:42:36,425 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:42:36,428 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:42:36,431 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:42:36,433 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 10:48:09,744 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:48:09,745 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:48:09,746 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:48:14,347 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:48:14,357 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 10:48:14,368 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: Cannot use `custom_model` option with the new API stack (RLModule and Learner APIs)! `custom_model` is part of the ModelV2 API and Policy API, which are not compatible with the new API stack. You can either deactivate the new stack via `config.api_stack( enable_rl_module_and_learner=False)`,or use the new stack (incl. RLModule API) and implement your custom model as an RLModule.
To suppress all validation errors, set `config.experimental(_validate_config=False)` at your own risk.
2025-08-05 10:48:16,155 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:335 - PPOAgent closed
2025-08-05 10:48:16,159 - __main__ - ERROR - quick_start.py:193 - Training failed: Cannot use `custom_model` option with the new API stack (RLModule and Learner APIs)! `custom_model` is part of the ModelV2 API and Policy API, which are not compatible with the new API stack. You can either deactivate the new stack via `config.api_stack( enable_rl_module_and_learner=False)`,or use the new stack (incl. RLModule API) and implement your custom model as an RLModule.
To suppress all validation errors, set `config.experimental(_validate_config=False)` at your own risk.
2025-08-05 10:48:16,166 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:48:16,169 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:48:16,172 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:48:16,175 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:48:16,178 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:48:16,180 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:48:16,183 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:48:16,186 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:48:16,188 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:48:16,190 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:48:16,192 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:48:16,194 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:48:16,196 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:48:16,198 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:48:16,200 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 10:57:18,817 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:57:18,818 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:57:18,819 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:57:24,120 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:57:24,127 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 10:57:28,655 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: list index out of range
2025-08-05 10:57:30,470 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 10:57:30,473 - __main__ - ERROR - quick_start.py:193 - Training failed: list index out of range
2025-08-05 10:57:30,476 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:57:30,480 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:57:30,482 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:57:30,485 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:57:30,488 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:57:30,490 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:57:30,493 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:57:30,496 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:57:30,498 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:57:30,501 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:57:30,503 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:57:30,506 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:57:30,508 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:57:30,511 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:57:30,514 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:03:30,992 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:03:30,993 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:03:30,995 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:03:35,280 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:03:35,289 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:03:39,833 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: list index out of range
2025-08-05 11:03:41,545 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:03:41,546 - __main__ - ERROR - quick_start.py:193 - Training failed: list index out of range
2025-08-05 11:03:41,548 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 11:03:41,550 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 11:03:41,551 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:03:41,552 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 11:03:41,553 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 11:03:41,555 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:03:41,556 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 11:03:41,557 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 11:03:41,558 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 11:03:41,559 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 11:03:41,561 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 11:03:41,562 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 11:03:41,563 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 11:03:41,564 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 11:03:41,565 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:06:49,701 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:06:49,703 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:06:49,704 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:06:53,914 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:06:53,923 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:07:02,028 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:07:02,033 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:07:02,038 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:07:02,041 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:07:02,045 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:07:02,048 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:07:02,157 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized MLPModel on device: cuda
2025-08-05 11:07:03,366 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cuda
2025-08-05 11:07:03,368 - rl_ct.models.mlp - INFO - mlp.py:132 - MLPModel initialized with 703,749 parameters
2025-08-05 11:07:03,370 - rl_ct.models.rllib_models - INFO - rllib_models.py:187 - RLlibMLPModel initialized with 703,749 parameters
2025-08-05 11:07:04,550 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu! (when checking argument for argument mat1 in method wrapper_CUDA_addmm)
2025-08-05 11:07:06,151 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:07:06,153 - __main__ - ERROR - quick_start.py:194 - Training failed: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu! (when checking argument for argument mat1 in method wrapper_CUDA_addmm)
2025-08-05 11:07:06,155 - __main__ - WARNING - quick_start.py:199 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 11:07:06,157 - __main__ - INFO - quick_start.py:205 - Running model evaluation...
2025-08-05 11:07:06,159 - __main__ - ERROR - quick_start.py:231 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:07:06,160 - __main__ - INFO - quick_start.py:232 - Skipping evaluation for demo
2025-08-05 11:07:06,161 - __main__ - INFO - quick_start.py:237 - Running backtesting...
2025-08-05 11:07:06,163 - __main__ - ERROR - quick_start.py:261 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:07:06,164 - __main__ - INFO - quick_start.py:262 - Skipping backtesting for demo
2025-08-05 11:07:06,165 - __main__ - INFO - quick_start.py:293 - ============================================================
2025-08-05 11:07:06,166 - __main__ - INFO - quick_start.py:294 - Quick start demo completed successfully!
2025-08-05 11:07:06,168 - __main__ - INFO - quick_start.py:295 - ============================================================
2025-08-05 11:07:06,169 - __main__ - INFO - quick_start.py:296 - Next steps:
2025-08-05 11:07:06,170 - __main__ - INFO - quick_start.py:297 - 1. Check results in 'results/' directory
2025-08-05 11:07:06,171 - __main__ - INFO - quick_start.py:298 - 2. Modify configs for your specific use case
2025-08-05 11:07:06,172 - __main__ - INFO - quick_start.py:299 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 11:07:06,174 - __main__ - INFO - quick_start.py:300 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:16:34,583 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:16:34,585 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:16:34,586 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:16:39,452 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:16:39,459 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:16:44,261 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: list index out of range
2025-08-05 11:16:46,120 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:16:46,123 - __main__ - ERROR - quick_start.py:194 - Training failed: list index out of range
2025-08-05 11:16:46,127 - __main__ - WARNING - quick_start.py:199 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 11:16:46,130 - __main__ - INFO - quick_start.py:205 - Running model evaluation...
2025-08-05 11:16:46,133 - __main__ - ERROR - quick_start.py:231 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:16:46,136 - __main__ - INFO - quick_start.py:232 - Skipping evaluation for demo
2025-08-05 11:16:46,139 - __main__ - INFO - quick_start.py:237 - Running backtesting...
2025-08-05 11:16:46,142 - __main__ - ERROR - quick_start.py:261 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:16:46,145 - __main__ - INFO - quick_start.py:262 - Skipping backtesting for demo
2025-08-05 11:16:46,147 - __main__ - INFO - quick_start.py:293 - ============================================================
2025-08-05 11:16:46,150 - __main__ - INFO - quick_start.py:294 - Quick start demo completed successfully!
2025-08-05 11:16:46,153 - __main__ - INFO - quick_start.py:295 - ============================================================
2025-08-05 11:16:46,155 - __main__ - INFO - quick_start.py:296 - Next steps:
2025-08-05 11:16:46,158 - __main__ - INFO - quick_start.py:297 - 1. Check results in 'results/' directory
2025-08-05 11:16:46,161 - __main__ - INFO - quick_start.py:298 - 2. Modify configs for your specific use case
2025-08-05 11:16:46,164 - __main__ - INFO - quick_start.py:299 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 11:16:46,167 - __main__ - INFO - quick_start.py:300 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:29:52,925 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:29:52,927 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:29:52,928 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:29:57,228 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:29:57,235 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:30:03,353 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:30:03,357 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:30:03,361 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:30:03,364 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:30:03,368 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:30:03,372 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:30:03,505 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:30:03,554 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:30:03,558 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:30:03,559 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:30:10,854 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:30:10,860 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:30:10,863 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:30:10,867 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:30:10,871 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:30:10,875 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 11:30:10,893 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:30:10,921 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:30:10,924 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:30:10,927 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:30:10,956 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 11:30:10,958 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 11:31:12,076 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: integer modulo by zero
2025-08-05 11:31:16,211 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:31:16,214 - __main__ - ERROR - quick_start.py:194 - Training failed: integer modulo by zero
2025-08-05 11:31:16,217 - __main__ - WARNING - quick_start.py:199 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 11:31:16,220 - __main__ - INFO - quick_start.py:205 - Running model evaluation...
2025-08-05 11:31:16,223 - __main__ - ERROR - quick_start.py:231 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:31:16,226 - __main__ - INFO - quick_start.py:232 - Skipping evaluation for demo
2025-08-05 11:31:16,228 - __main__ - INFO - quick_start.py:237 - Running backtesting...
2025-08-05 11:31:16,231 - __main__ - ERROR - quick_start.py:261 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:31:16,234 - __main__ - INFO - quick_start.py:262 - Skipping backtesting for demo
2025-08-05 11:31:16,236 - __main__ - INFO - quick_start.py:293 - ============================================================
2025-08-05 11:31:16,239 - __main__ - INFO - quick_start.py:294 - Quick start demo completed successfully!
2025-08-05 11:31:16,241 - __main__ - INFO - quick_start.py:295 - ============================================================
2025-08-05 11:31:16,244 - __main__ - INFO - quick_start.py:296 - Next steps:
2025-08-05 11:31:16,246 - __main__ - INFO - quick_start.py:297 - 1. Check results in 'results/' directory
2025-08-05 11:31:16,249 - __main__ - INFO - quick_start.py:298 - 2. Modify configs for your specific use case
2025-08-05 11:31:16,252 - __main__ - INFO - quick_start.py:299 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 11:31:16,254 - __main__ - INFO - quick_start.py:300 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:35:41,227 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:35:41,228 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:35:41,229 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:35:45,551 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:35:45,560 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:35:52,053 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:35:52,057 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:35:52,061 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:35:52,064 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:35:52,068 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:35:52,071 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:35:52,174 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:35:52,219 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:35:52,221 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:35:52,222 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:35:59,937 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:35:59,950 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:35:59,953 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:35:59,956 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:35:59,959 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:35:59,963 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 11:35:59,978 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:35:59,990 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:35:59,992 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:35:59,993 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:36:00,017 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 11:36:00,019 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 11:37:57,684 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:37:57,686 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:37:57,687 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:38:01,601 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:38:01,607 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:38:07,622 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:38:07,625 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:38:07,629 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:38:07,632 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:38:07,635 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:38:07,639 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:38:07,745 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:38:07,796 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:38:07,798 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:38:07,799 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:38:14,857 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:38:14,860 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:38:14,864 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:38:14,867 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:38:14,870 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:38:14,873 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 11:38:14,889 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:38:14,908 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:38:14,911 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:38:14,914 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:38:14,943 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 11:38:14,946 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 11:38:35,284 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:38:35,285 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:38:35,287 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:38:39,459 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:38:39,469 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:38:46,291 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:38:46,294 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:38:46,298 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:38:46,302 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:38:46,305 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:38:46,309 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:38:46,417 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:38:46,472 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:38:46,473 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:38:46,475 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:38:53,775 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:38:53,779 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:38:53,782 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:38:53,786 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:38:53,789 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:38:53,792 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 11:38:53,807 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:38:53,827 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:38:53,829 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:38:53,831 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:38:53,857 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 11:38:53,858 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 11:40:42,089 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:40:42,091 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:40:42,092 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:40:46,152 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:40:46,159 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:40:52,193 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:40:52,196 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:40:52,200 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:40:52,203 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:40:52,206 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:40:52,210 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:40:52,325 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:40:52,366 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:40:52,368 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:40:52,369 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:40:59,131 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:40:59,135 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:40:59,138 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:40:59,141 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:40:59,144 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:40:59,148 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 11:40:59,169 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:40:59,190 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:40:59,192 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:40:59,193 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:40:59,221 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 11:40:59,223 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 11:42:02,787 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:43:21,048 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:43:21,050 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:43:21,051 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:43:25,008 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:43:25,016 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:43:30,555 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:43:30,557 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:43:30,559 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:43:30,560 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:43:30,562 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:43:30,564 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:43:30,670 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:43:30,720 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:43:30,722 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:43:30,723 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:43:37,361 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:43:37,365 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:43:37,368 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:43:37,371 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:43:37,374 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:43:37,378 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 11:43:37,392 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:43:37,418 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:43:37,420 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:43:37,423 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:43:37,452 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 11:43:37,454 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 11:44:39,079 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: integer modulo by zero
2025-08-05 11:44:43,015 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:44:43,019 - __main__ - ERROR - quick_start.py:195 - Training failed: integer modulo by zero
2025-08-05 11:44:43,021 - __main__ - WARNING - quick_start.py:200 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 11:44:43,024 - __main__ - INFO - quick_start.py:206 - Running model evaluation...
2025-08-05 11:44:43,027 - __main__ - ERROR - quick_start.py:232 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:44:43,030 - __main__ - INFO - quick_start.py:233 - Skipping evaluation for demo
2025-08-05 11:44:43,033 - __main__ - INFO - quick_start.py:238 - Running backtesting...
2025-08-05 11:44:43,035 - __main__ - ERROR - quick_start.py:262 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:44:43,038 - __main__ - INFO - quick_start.py:263 - Skipping backtesting for demo
2025-08-05 11:44:43,041 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 11:44:43,043 - __main__ - INFO - quick_start.py:295 - Quick start demo completed successfully!
2025-08-05 11:44:43,046 - __main__ - INFO - quick_start.py:296 - ============================================================
2025-08-05 11:44:43,048 - __main__ - INFO - quick_start.py:297 - Next steps:
2025-08-05 11:44:43,050 - __main__ - INFO - quick_start.py:298 - 1. Check results in 'results/' directory
2025-08-05 11:44:43,053 - __main__ - INFO - quick_start.py:299 - 2. Modify configs for your specific use case
2025-08-05 11:44:43,056 - __main__ - INFO - quick_start.py:300 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 11:44:43,058 - __main__ - INFO - quick_start.py:301 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:55:34,474 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:55:34,475 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:55:34,476 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:55:39,911 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:55:39,917 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:55:45,990 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:55:45,993 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:55:45,996 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:55:45,998 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:55:46,001 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:55:46,004 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:55:46,087 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:55:46,123 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:55:46,124 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:55:46,126 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:55:53,338 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:55:53,340 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:55:53,342 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:55:53,343 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:55:53,345 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:55:53,346 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 11:55:53,354 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 11:55:53,383 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 11:55:53,385 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 11:55:53,386 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 11:55:53,450 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 11:55:53,452 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 11:56:56,594 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 15:27:37,502 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 15:27:37,504 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 15:27:37,505 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 15:27:41,733 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 15:27:41,741 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 15:27:46,870 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 15:27:53,525 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 15:27:53,527 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 15:27:53,528 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 15:27:57,759 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 15:27:57,766 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 15:28:04,364 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:28:04,368 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:28:04,372 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:28:04,375 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:28:04,378 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:28:04,382 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 15:28:04,445 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 15:28:04,490 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 15:28:04,491 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 15:28:04,493 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 15:28:11,358 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:28:11,361 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:28:11,364 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:28:11,368 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:28:11,371 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:28:11,374 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 15:28:11,390 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 15:28:11,407 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 15:28:11,409 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 15:28:11,411 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 15:28:11,434 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 15:28:11,436 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 15:29:12,853 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: integer modulo by zero
2025-08-05 15:29:16,953 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 15:29:16,956 - __main__ - ERROR - quick_start.py:195 - Training failed: integer modulo by zero
2025-08-05 15:29:16,959 - __main__ - WARNING - quick_start.py:200 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 15:29:16,961 - __main__ - INFO - quick_start.py:206 - Running model evaluation...
2025-08-05 15:29:16,963 - __main__ - ERROR - quick_start.py:232 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 15:29:16,965 - __main__ - INFO - quick_start.py:233 - Skipping evaluation for demo
2025-08-05 15:29:16,967 - __main__ - INFO - quick_start.py:238 - Running backtesting...
2025-08-05 15:29:16,969 - __main__ - ERROR - quick_start.py:262 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 15:29:16,971 - __main__ - INFO - quick_start.py:263 - Skipping backtesting for demo
2025-08-05 15:29:16,973 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 15:29:16,975 - __main__ - INFO - quick_start.py:295 - Quick start demo completed successfully!
2025-08-05 15:29:16,977 - __main__ - INFO - quick_start.py:296 - ============================================================
2025-08-05 15:29:16,978 - __main__ - INFO - quick_start.py:297 - Next steps:
2025-08-05 15:29:16,980 - __main__ - INFO - quick_start.py:298 - 1. Check results in 'results/' directory
2025-08-05 15:29:16,982 - __main__ - INFO - quick_start.py:299 - 2. Modify configs for your specific use case
2025-08-05 15:29:16,985 - __main__ - INFO - quick_start.py:300 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 15:29:16,987 - __main__ - INFO - quick_start.py:301 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 15:32:19,457 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 15:32:19,459 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 15:32:19,460 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 15:32:24,667 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 15:32:24,677 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 15:32:30,497 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:32:30,500 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:32:30,504 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:32:30,508 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:32:30,511 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:32:30,516 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 15:32:30,580 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 15:32:30,635 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 15:32:30,637 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 15:32:30,638 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 15:32:37,822 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:32:37,824 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:32:37,827 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:32:37,829 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:32:37,831 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:32:37,833 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 15:32:37,844 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 15:32:37,857 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 15:32:37,858 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 15:32:37,859 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 15:32:37,879 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 15:32:37,881 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 15:33:30,302 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 4000 - Reward: 0.00 - Length: 0.0
2025-08-05 15:34:24,770 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 8000 - Reward: 0.00 - Length: 0.0
2025-08-05 15:34:24,773 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 15:34:24,775 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 15:34:24,778 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:34:24,779 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:34:24,781 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:34:24,783 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:34:24,785 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:34:24,787 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 15:34:26,855 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 15:34:26,859 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: -0.37 ± 0.67
2025-08-05 15:34:26,860 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 50.0
2025-08-05 15:34:26,862 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: -0.37 ± 0.67
2025-08-05 15:34:26,883 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/best_model_step_10000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.3592118280549203), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.014617050950220156), 'policy_loss': np.float64(-0.007680436524171983), 'vf_loss': np.float64(0.00613839581098047), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.00883772891360305), 'entropy': np.float64(1.2657130925886093), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(465.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'env_runners': {'episode_reward_max': 0.046050657254845154, 'episode_reward_min': -0.0873763093287002, 'episode_reward_mean': np.float64(-0.014493957217067177), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 8000, 'policy_reward_min': {'default_policy': np.float64(-0.0873763093287002)}, 'policy_reward_max': {'default_policy': np.float64(0.046050657254845154)}, 'policy_reward_mean': {'default_policy': np.float64(-0.014493957217067177)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009714180754735239, -0.010944758740510596, -0.004742186125249822, -0.006875214276490647, -0.014109135383263104, -0.02191101036803977, -0.02564541502591449, -0.039871437264611925, 0.006260028434742614, -0.02997646336639232, 0.010498206600940415, -0.0030471423258917954, -0.0022913590563493104, -0.0150791473701978, -0.04988864698810995, -0.007725626501787559, 0.046050657254845154, 0.015179787063995658, -0.00427998879028725, -0.017196133050228483, -0.005091830665752554, -0.009656016213422417, 0.012226511473251989, 0.0005859134728660679, -0.008288801766483833, 0.016506516972394476, 0.015969490695129315, -0.01169668279193623, -0.030538654516000463, -0.012030486402499457, -0.03455054459433443, 0.009602457087258936, -0.006908005020113697, -0.014605721061897512, -0.02854446694669119, -0.01429812893279971, -0.03490702387644565, -0.062033058455873424, -0.003531118595878603, 0.0031191069974325254], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009714180754735239, -0.010944758740510596, -0.004742186125249822, -0.006875214276490647, -0.014109135383263104, -0.02191101036803977, -0.02564541502591449, -0.039871437264611925, 0.006260028434742614, -0.02997646336639232, 0.010498206600940415, -0.0030471423258917954, -0.0022913590563493104, -0.0150791473701978, -0.04988864698810995, -0.007725626501787559, 0.046050657254845154, 0.015179787063995658, -0.00427998879028725, -0.017196133050228483, -0.005091830665752554, -0.009656016213422417, 0.012226511473251989, 0.0005859134728660679, -0.008288801766483833, 0.016506516972394476, 0.015969490695129315, -0.01169668279193623, -0.030538654516000463, -0.012030486402499457, -0.03455054459433443, 0.009602457087258936, -0.006908005020113697, -0.014605721061897512, -0.02854446694669119, -0.01429812893279971, -0.03490702387644565, -0.062033058455873424, -0.003531118595878603, 0.0031191069974325254]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.2507528492866061), 'mean_inference_ms': np.float64(1.973770017192941), 'mean_action_processing_ms': np.float64(0.07505725334017219), 'mean_env_wait_ms': np.float64(0.10716509746120428), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.0037351250648498535), 'StateBufferConnector_ms': np.float64(0.0021857023239135742), 'ViewRequirementAgentConnector_ms': np.float64(0.06815850734710693)}, 'num_episodes': 40, 'episode_return_max': 0.046050657254845154, 'episode_return_min': -0.0873763093287002, 'episode_return_mean': np.float64(-0.014493957217067177), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 73.48321034722785, 'num_env_steps_trained_throughput_per_sec': 73.48321034722785, 'timesteps_total': 8000, 'num_env_steps_sampled_lifetime': 8000, 'num_agent_steps_sampled_lifetime': 8000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 8000, 'timers': {'training_iteration_time_ms': 53412.675, 'restore_workers_time_ms': 0.025, 'training_step_time_ms': 53412.564, 'sample_time_ms': 5030.752, 'load_time_ms': 2.835, 'load_throughput': 1411095.168, 'learn_time_ms': 48365.167, 'learn_throughput': 82.704, 'synch_weights_time_ms': 12.705}, 'counters': {'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'done': False, 'training_iteration': 2, 'trial_id': 'default', 'date': '2025-08-05_15-34-24', 'timestamp': 1754379264, 'time_this_iter_s': 54.44149303436279, 'time_total_s': 106.84038138389587, 'pid': 3279764, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 20, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7fed055d02c0>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 106.84038138389587, 'iterations_since_restore': 2, 'perf': {'cpu_util_percent': np.float64(45.90641025641025), 'ram_util_percent': np.float64(53.40128205128205)}})
2025-08-05 15:34:26,950 - rl_ct.scripts.train - INFO - train.py:125 - New best model saved: checkpoints/quick_start_demo/best_model_step_10000.zip
2025-08-05 15:34:26,967 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_10000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.3592118280549203), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.014617050950220156), 'policy_loss': np.float64(-0.007680436524171983), 'vf_loss': np.float64(0.00613839581098047), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.00883772891360305), 'entropy': np.float64(1.2657130925886093), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(465.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'env_runners': {'episode_reward_max': 0.046050657254845154, 'episode_reward_min': -0.0873763093287002, 'episode_reward_mean': np.float64(-0.014493957217067177), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 8000, 'policy_reward_min': {'default_policy': np.float64(-0.0873763093287002)}, 'policy_reward_max': {'default_policy': np.float64(0.046050657254845154)}, 'policy_reward_mean': {'default_policy': np.float64(-0.014493957217067177)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009714180754735239, -0.010944758740510596, -0.004742186125249822, -0.006875214276490647, -0.014109135383263104, -0.02191101036803977, -0.02564541502591449, -0.039871437264611925, 0.006260028434742614, -0.02997646336639232, 0.010498206600940415, -0.0030471423258917954, -0.0022913590563493104, -0.0150791473701978, -0.04988864698810995, -0.007725626501787559, 0.046050657254845154, 0.015179787063995658, -0.00427998879028725, -0.017196133050228483, -0.005091830665752554, -0.009656016213422417, 0.012226511473251989, 0.0005859134728660679, -0.008288801766483833, 0.016506516972394476, 0.015969490695129315, -0.01169668279193623, -0.030538654516000463, -0.012030486402499457, -0.03455054459433443, 0.009602457087258936, -0.006908005020113697, -0.014605721061897512, -0.02854446694669119, -0.01429812893279971, -0.03490702387644565, -0.062033058455873424, -0.003531118595878603, 0.0031191069974325254], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009714180754735239, -0.010944758740510596, -0.004742186125249822, -0.006875214276490647, -0.014109135383263104, -0.02191101036803977, -0.02564541502591449, -0.039871437264611925, 0.006260028434742614, -0.02997646336639232, 0.010498206600940415, -0.0030471423258917954, -0.0022913590563493104, -0.0150791473701978, -0.04988864698810995, -0.007725626501787559, 0.046050657254845154, 0.015179787063995658, -0.00427998879028725, -0.017196133050228483, -0.005091830665752554, -0.009656016213422417, 0.012226511473251989, 0.0005859134728660679, -0.008288801766483833, 0.016506516972394476, 0.015969490695129315, -0.01169668279193623, -0.030538654516000463, -0.012030486402499457, -0.03455054459433443, 0.009602457087258936, -0.006908005020113697, -0.014605721061897512, -0.02854446694669119, -0.01429812893279971, -0.03490702387644565, -0.062033058455873424, -0.003531118595878603, 0.0031191069974325254]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.2507528492866061), 'mean_inference_ms': np.float64(1.973770017192941), 'mean_action_processing_ms': np.float64(0.07505725334017219), 'mean_env_wait_ms': np.float64(0.10716509746120428), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.0037351250648498535), 'StateBufferConnector_ms': np.float64(0.0021857023239135742), 'ViewRequirementAgentConnector_ms': np.float64(0.06815850734710693)}, 'num_episodes': 40, 'episode_return_max': 0.046050657254845154, 'episode_return_min': -0.0873763093287002, 'episode_return_mean': np.float64(-0.014493957217067177), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 73.48321034722785, 'num_env_steps_trained_throughput_per_sec': 73.48321034722785, 'timesteps_total': 8000, 'num_env_steps_sampled_lifetime': 8000, 'num_agent_steps_sampled_lifetime': 8000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 8000, 'timers': {'training_iteration_time_ms': 53412.675, 'restore_workers_time_ms': 0.025, 'training_step_time_ms': 53412.564, 'sample_time_ms': 5030.752, 'load_time_ms': 2.835, 'load_throughput': 1411095.168, 'learn_time_ms': 48365.167, 'learn_throughput': 82.704, 'synch_weights_time_ms': 12.705}, 'counters': {'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'done': False, 'training_iteration': 2, 'trial_id': 'default', 'date': '2025-08-05_15-34-24', 'timestamp': 1754379264, 'time_this_iter_s': 54.44149303436279, 'time_total_s': 106.84038138389587, 'pid': 3279764, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 20, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7fed055d02c0>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 106.84038138389587, 'iterations_since_restore': 2, 'perf': {'cpu_util_percent': np.float64(45.90641025641025), 'ram_util_percent': np.float64(53.40128205128205)}})
2025-08-05 15:34:27,594 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 15:35:19,407 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 12000 - Reward: 0.00 - Length: 0.0
2025-08-05 15:36:12,226 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 16000 - Reward: 0.00 - Length: 0.0
2025-08-05 15:36:12,230 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 15:36:12,233 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 15:36:12,236 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:36:12,237 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:36:12,239 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:36:12,241 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:36:12,243 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:36:12,245 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 15:36:15,527 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 15:36:15,529 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: 0.00 ± 0.00
2025-08-05 15:36:15,531 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 100.0
2025-08-05 15:36:15,532 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: 0.00 ± 0.00
2025-08-05 15:36:15,548 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/best_model_step_20000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.28994807259690375), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.016585552382222826), 'policy_loss': np.float64(-0.006905161583375547), 'vf_loss': np.float64(0.0027706043006131245), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.006035376396480828), 'entropy': np.float64(1.2876306049285395), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(1085.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000}, 'env_runners': {'episode_reward_max': 0.033122670695380566, 'episode_reward_min': -0.08057998934897614, 'episode_reward_mean': np.float64(-0.00874627258480448), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 10000, 'policy_reward_min': {'default_policy': np.float64(-0.08057998934897614)}, 'policy_reward_max': {'default_policy': np.float64(0.033122670695380566)}, 'policy_reward_mean': {'default_policy': np.float64(-0.00874627258480448)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.005091830665752554, -0.009656016213422417, 0.012226511473251989, 0.0005859134728660679, -0.008288801766483833, 0.016506516972394476, 0.015969490695129315, -0.01169668279193623, -0.030538654516000463, -0.012030486402499457, -0.03455054459433443, 0.009602457087258936, -0.006908005020113697, -0.014605721061897512, -0.02854446694669119, -0.01429812893279971, -0.03490702387644565, -0.062033058455873424, -0.003531118595878603, 0.0031191069974325254, -0.08057998934897614, -0.010498937813982916, -0.002918286346607464, -0.0001708263230232182, -0.018950157032090326, -0.013352562451354761, -0.040650636721391506, -0.0395090773820708, -0.003431623034001529, -0.006972479923103894, 0.0073906190744205125, 0.016254536179905296, -0.016309730570704624, -0.049873343753686405, 0.005189860001935678, -0.03427549201931657, -0.00813913342416872, -0.009655805119531357, -0.011192049681055118, -0.012874781720248673, -0.026430963011400774, 0.005126015856435577, -0.009584324657567964, -0.025152844134257425, -0.029393080484507676, 0.001060771875361348, 0.005663540237994239, 0.0005236287081667179, -0.012434551275458004, 0.004471614544560517, -0.00998238270779326, -0.013044811470259413, -0.010268975127092772, -0.0110059306934364, 0.014296982339436482, 0.0008522322431055988, 0.011449836604726593, 0.01124902608108839, 0.01634935991915999, 0.008109432970124796, -0.022494102710884326, -0.012482292332999783, 0.009110508249705423, -0.03218256591993877, -0.0021815282954701087, -0.014513548419540709, -0.004604872558953877, -0.007075610379868017, 0.01165457608053009, 0.009741203009646406, -0.0004909978879667903, -0.016117601641847983, -0.003743447933965754, -0.01053989141739742, -0.019902242075132793, 0.0022553474823268915, -0.003910664052670279, -0.00451379634524296, -0.01753271661670019, 0.0028187752480241975, 0.033122670695380566, -0.006422494610347895, 0.0065606284489129465, 0.0023731904660097593, 0.02529645716448017, -0.0015503489953522208, -0.00885813674918088, -0.011680636883665215, 0.003103597931480663, -0.0032887125984264118, -0.003782921451400712, 0.0017666831830142806, -0.054861979732239205, -0.004431203975931452, -0.027944589877879164, -0.02219355694838295, 9.477267471530437e-05, -0.04450201289971312, 0.0017677469556832432, -0.005155080026797168], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.005091830665752554, -0.009656016213422417, 0.012226511473251989, 0.0005859134728660679, -0.008288801766483833, 0.016506516972394476, 0.015969490695129315, -0.01169668279193623, -0.030538654516000463, -0.012030486402499457, -0.03455054459433443, 0.009602457087258936, -0.006908005020113697, -0.014605721061897512, -0.02854446694669119, -0.01429812893279971, -0.03490702387644565, -0.062033058455873424, -0.003531118595878603, 0.0031191069974325254, -0.08057998934897614, -0.010498937813982916, -0.002918286346607464, -0.0001708263230232182, -0.018950157032090326, -0.013352562451354761, -0.040650636721391506, -0.0395090773820708, -0.003431623034001529, -0.006972479923103894, 0.0073906190744205125, 0.016254536179905296, -0.016309730570704624, -0.049873343753686405, 0.005189860001935678, -0.03427549201931657, -0.00813913342416872, -0.009655805119531357, -0.011192049681055118, -0.012874781720248673, -0.026430963011400774, 0.005126015856435577, -0.009584324657567964, -0.025152844134257425, -0.029393080484507676, 0.001060771875361348, 0.005663540237994239, 0.0005236287081667179, -0.012434551275458004, 0.004471614544560517, -0.00998238270779326, -0.013044811470259413, -0.010268975127092772, -0.0110059306934364, 0.014296982339436482, 0.0008522322431055988, 0.011449836604726593, 0.01124902608108839, 0.01634935991915999, 0.008109432970124796, -0.022494102710884326, -0.012482292332999783, 0.009110508249705423, -0.03218256591993877, -0.0021815282954701087, -0.014513548419540709, -0.004604872558953877, -0.007075610379868017, 0.01165457608053009, 0.009741203009646406, -0.0004909978879667903, -0.016117601641847983, -0.003743447933965754, -0.01053989141739742, -0.019902242075132793, 0.0022553474823268915, -0.003910664052670279, -0.00451379634524296, -0.01753271661670019, 0.0028187752480241975, 0.033122670695380566, -0.006422494610347895, 0.0065606284489129465, 0.0023731904660097593, 0.02529645716448017, -0.0015503489953522208, -0.00885813674918088, -0.011680636883665215, 0.003103597931480663, -0.0032887125984264118, -0.003782921451400712, 0.0017666831830142806, -0.054861979732239205, -0.004431203975931452, -0.027944589877879164, -0.02219355694838295, 9.477267471530437e-05, -0.04450201289971312, 0.0017677469556832432, -0.005155080026797168]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.24689990180596014), 'mean_inference_ms': np.float64(1.9303930086968029), 'mean_action_processing_ms': np.float64(0.07363192323713395), 'mean_env_wait_ms': np.float64(0.10410036255463444), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.002588510513305664), 'StateBufferConnector_ms': np.float64(0.0022194385528564453), 'ViewRequirementAgentConnector_ms': np.float64(0.06996846199035645)}, 'num_episodes': 40, 'episode_return_max': 0.033122670695380566, 'episode_return_min': -0.08057998934897614, 'episode_return_mean': np.float64(-0.00874627258480448), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000, 'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 75.78582535657173, 'num_env_steps_trained_throughput_per_sec': 75.78582535657173, 'timesteps_total': 16000, 'num_env_steps_sampled_lifetime': 16000, 'num_agent_steps_sampled_lifetime': 16000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 16000, 'timers': {'training_iteration_time_ms': 52844.567, 'restore_workers_time_ms': 0.053, 'training_step_time_ms': 52844.404, 'sample_time_ms': 5186.611, 'load_time_ms': 3.689, 'load_throughput': 1084272.276, 'learn_time_ms': 47637.749, 'learn_throughput': 83.967, 'synch_weights_time_ms': 15.47}, 'counters': {'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000}, 'done': False, 'training_iteration': 4, 'trial_id': 'default', 'date': '2025-08-05_15-36-12', 'timestamp': 1754379372, 'time_this_iter_s': 52.791281938552856, 'time_total_s': 211.41605639457703, 'pid': 3279764, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 20, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7fed055d02c0>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 211.41605639457703, 'iterations_since_restore': 4, 'perf': {'cpu_util_percent': np.float64(44.41866666666667), 'ram_util_percent': np.float64(53.58933333333333)}})
2025-08-05 15:36:15,609 - rl_ct.scripts.train - INFO - train.py:125 - New best model saved: checkpoints/quick_start_demo/best_model_step_20000.zip
2025-08-05 15:36:15,622 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_20000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.28994807259690375), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.016585552382222826), 'policy_loss': np.float64(-0.006905161583375547), 'vf_loss': np.float64(0.0027706043006131245), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.006035376396480828), 'entropy': np.float64(1.2876306049285395), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(1085.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000}, 'env_runners': {'episode_reward_max': 0.033122670695380566, 'episode_reward_min': -0.08057998934897614, 'episode_reward_mean': np.float64(-0.00874627258480448), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 10000, 'policy_reward_min': {'default_policy': np.float64(-0.08057998934897614)}, 'policy_reward_max': {'default_policy': np.float64(0.033122670695380566)}, 'policy_reward_mean': {'default_policy': np.float64(-0.00874627258480448)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.005091830665752554, -0.009656016213422417, 0.012226511473251989, 0.0005859134728660679, -0.008288801766483833, 0.016506516972394476, 0.015969490695129315, -0.01169668279193623, -0.030538654516000463, -0.012030486402499457, -0.03455054459433443, 0.009602457087258936, -0.006908005020113697, -0.014605721061897512, -0.02854446694669119, -0.01429812893279971, -0.03490702387644565, -0.062033058455873424, -0.003531118595878603, 0.0031191069974325254, -0.08057998934897614, -0.010498937813982916, -0.002918286346607464, -0.0001708263230232182, -0.018950157032090326, -0.013352562451354761, -0.040650636721391506, -0.0395090773820708, -0.003431623034001529, -0.006972479923103894, 0.0073906190744205125, 0.016254536179905296, -0.016309730570704624, -0.049873343753686405, 0.005189860001935678, -0.03427549201931657, -0.00813913342416872, -0.009655805119531357, -0.011192049681055118, -0.012874781720248673, -0.026430963011400774, 0.005126015856435577, -0.009584324657567964, -0.025152844134257425, -0.029393080484507676, 0.001060771875361348, 0.005663540237994239, 0.0005236287081667179, -0.012434551275458004, 0.004471614544560517, -0.00998238270779326, -0.013044811470259413, -0.010268975127092772, -0.0110059306934364, 0.014296982339436482, 0.0008522322431055988, 0.011449836604726593, 0.01124902608108839, 0.01634935991915999, 0.008109432970124796, -0.022494102710884326, -0.012482292332999783, 0.009110508249705423, -0.03218256591993877, -0.0021815282954701087, -0.014513548419540709, -0.004604872558953877, -0.007075610379868017, 0.01165457608053009, 0.009741203009646406, -0.0004909978879667903, -0.016117601641847983, -0.003743447933965754, -0.01053989141739742, -0.019902242075132793, 0.0022553474823268915, -0.003910664052670279, -0.00451379634524296, -0.01753271661670019, 0.0028187752480241975, 0.033122670695380566, -0.006422494610347895, 0.0065606284489129465, 0.0023731904660097593, 0.02529645716448017, -0.0015503489953522208, -0.00885813674918088, -0.011680636883665215, 0.003103597931480663, -0.0032887125984264118, -0.003782921451400712, 0.0017666831830142806, -0.054861979732239205, -0.004431203975931452, -0.027944589877879164, -0.02219355694838295, 9.477267471530437e-05, -0.04450201289971312, 0.0017677469556832432, -0.005155080026797168], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.005091830665752554, -0.009656016213422417, 0.012226511473251989, 0.0005859134728660679, -0.008288801766483833, 0.016506516972394476, 0.015969490695129315, -0.01169668279193623, -0.030538654516000463, -0.012030486402499457, -0.03455054459433443, 0.009602457087258936, -0.006908005020113697, -0.014605721061897512, -0.02854446694669119, -0.01429812893279971, -0.03490702387644565, -0.062033058455873424, -0.003531118595878603, 0.0031191069974325254, -0.08057998934897614, -0.010498937813982916, -0.002918286346607464, -0.0001708263230232182, -0.018950157032090326, -0.013352562451354761, -0.040650636721391506, -0.0395090773820708, -0.003431623034001529, -0.006972479923103894, 0.0073906190744205125, 0.016254536179905296, -0.016309730570704624, -0.049873343753686405, 0.005189860001935678, -0.03427549201931657, -0.00813913342416872, -0.009655805119531357, -0.011192049681055118, -0.012874781720248673, -0.026430963011400774, 0.005126015856435577, -0.009584324657567964, -0.025152844134257425, -0.029393080484507676, 0.001060771875361348, 0.005663540237994239, 0.0005236287081667179, -0.012434551275458004, 0.004471614544560517, -0.00998238270779326, -0.013044811470259413, -0.010268975127092772, -0.0110059306934364, 0.014296982339436482, 0.0008522322431055988, 0.011449836604726593, 0.01124902608108839, 0.01634935991915999, 0.008109432970124796, -0.022494102710884326, -0.012482292332999783, 0.009110508249705423, -0.03218256591993877, -0.0021815282954701087, -0.014513548419540709, -0.004604872558953877, -0.007075610379868017, 0.01165457608053009, 0.009741203009646406, -0.0004909978879667903, -0.016117601641847983, -0.003743447933965754, -0.01053989141739742, -0.019902242075132793, 0.0022553474823268915, -0.003910664052670279, -0.00451379634524296, -0.01753271661670019, 0.0028187752480241975, 0.033122670695380566, -0.006422494610347895, 0.0065606284489129465, 0.0023731904660097593, 0.02529645716448017, -0.0015503489953522208, -0.00885813674918088, -0.011680636883665215, 0.003103597931480663, -0.0032887125984264118, -0.003782921451400712, 0.0017666831830142806, -0.054861979732239205, -0.004431203975931452, -0.027944589877879164, -0.02219355694838295, 9.477267471530437e-05, -0.04450201289971312, 0.0017677469556832432, -0.005155080026797168]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.24689990180596014), 'mean_inference_ms': np.float64(1.9303930086968029), 'mean_action_processing_ms': np.float64(0.07363192323713395), 'mean_env_wait_ms': np.float64(0.10410036255463444), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.002588510513305664), 'StateBufferConnector_ms': np.float64(0.0022194385528564453), 'ViewRequirementAgentConnector_ms': np.float64(0.06996846199035645)}, 'num_episodes': 40, 'episode_return_max': 0.033122670695380566, 'episode_return_min': -0.08057998934897614, 'episode_return_mean': np.float64(-0.00874627258480448), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000, 'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 75.78582535657173, 'num_env_steps_trained_throughput_per_sec': 75.78582535657173, 'timesteps_total': 16000, 'num_env_steps_sampled_lifetime': 16000, 'num_agent_steps_sampled_lifetime': 16000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 16000, 'timers': {'training_iteration_time_ms': 52844.567, 'restore_workers_time_ms': 0.053, 'training_step_time_ms': 52844.404, 'sample_time_ms': 5186.611, 'load_time_ms': 3.689, 'load_throughput': 1084272.276, 'learn_time_ms': 47637.749, 'learn_throughput': 83.967, 'synch_weights_time_ms': 15.47}, 'counters': {'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000}, 'done': False, 'training_iteration': 4, 'trial_id': 'default', 'date': '2025-08-05_15-36-12', 'timestamp': 1754379372, 'time_this_iter_s': 52.791281938552856, 'time_total_s': 211.41605639457703, 'pid': 3279764, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 20, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7fed055d02c0>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 211.41605639457703, 'iterations_since_restore': 4, 'perf': {'cpu_util_percent': np.float64(44.41866666666667), 'ram_util_percent': np.float64(53.58933333333333)}})
2025-08-05 15:36:16,272 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 15:37:10,344 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 20000 - Reward: 0.00 - Length: 0.0
2025-08-05 15:38:03,161 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 24000 - Reward: 0.00 - Length: 0.0
2025-08-05 15:38:03,163 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 15:38:03,165 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 15:38:03,167 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:38:03,169 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:38:03,170 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:38:03,172 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:38:03,173 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:38:03,175 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 15:38:06,106 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 15:38:06,108 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: 0.00 ± 0.00
2025-08-05 15:38:06,110 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 100.0
2025-08-05 15:38:06,111 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: 0.00 ± 0.00
2025-08-05 15:38:06,126 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_30000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.22876592804827997), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.023845661907351667), 'policy_loss': np.float64(-0.014566322102121288), 'vf_loss': np.float64(0.0015786125326544167), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.00809058312612075), 'entropy': np.float64(1.2495820495390122), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(1705.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 24000, 'num_env_steps_trained': 24000, 'num_agent_steps_sampled': 24000, 'num_agent_steps_trained': 24000}, 'env_runners': {'episode_reward_max': 0.033122670695380566, 'episode_reward_min': -0.07743673023784685, 'episode_reward_mean': np.float64(-0.00824668524996413), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 10000, 'policy_reward_min': {'default_policy': np.float64(-0.07743673023784685)}, 'policy_reward_max': {'default_policy': np.float64(0.033122670695380566)}, 'policy_reward_mean': {'default_policy': np.float64(-0.00824668524996413)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [0.033122670695380566, -0.006422494610347895, 0.0065606284489129465, 0.0023731904660097593, 0.02529645716448017, -0.0015503489953522208, -0.00885813674918088, -0.011680636883665215, 0.003103597931480663, -0.0032887125984264118, -0.003782921451400712, 0.0017666831830142806, -0.054861979732239205, -0.004431203975931452, -0.027944589877879164, -0.02219355694838295, 9.477267471530437e-05, -0.04450201289971312, 0.0017677469556832432, -0.005155080026797168, -0.07743673023784685, -0.04938686537323632, -0.020907190143227356, -0.012034373232683734, -0.00015134441708200552, -0.001182710809050351, -0.003173987684000677, 0.0014795748228695563, 0.0073534900832690365, -0.0008354489267667024, 0.005994501551087671, -0.001949654470300469, -0.025250435448139805, -0.007405976396232998, -0.01364334404516715, -0.006770644820588019, -0.010306546235516472, -0.011511159816227353, -0.014546058678045365, -0.023990360141974858, 0.0021104946413032033, 0.004507719925984088, -0.03336452336797725, 0.00975795712873447, -0.02700246248983946, -0.012851437640422602, -0.006881308811313375, 0.0121542640485479, -0.003466398700888551, -0.0027956197687914984, 0.006550841585410281, -0.014770631204515145, -0.010865934796680894, 0.009154312128834259, -0.001381663756948072, -0.013483048977708967, -0.02183533767161601, -0.02700077518757424, -0.010096966159120185, 0.0006548018896803537, -0.013370796731764865, -0.04856916230201694, 0.003084337112093458, -0.0002001478381022416, -0.018929333253243724, -0.009315709605782468, -0.008477242435249633, -0.008113477059228961, -0.04131690222913414, -0.014994397497922878, -0.01915438574021854, -0.0073147036449580476, -0.001109584190294513, -0.013207670347384314, -0.0032800951696012257, -0.001245808284429193, 0.0006095804852670628, 0.003343324988975929, -0.02460481964367448, 0.0019205916116679794, 0.0049708360816970425, -0.003621874278616497, 0.000340155539984479, -0.0007228877611627701, -0.0031879083954881645, -0.01495513131087924, -0.008615409755051996, -0.010496532884232522, 0.0011720295431539435, -0.00016695054567089813, -0.0023622824669146322, 0.004736660980975294, -0.019945183629219078, -0.0015265639554185983, -9.342410814471708e-05, -0.0060222592335275205, -0.010818014323492617, 0.0013811222678725577, 0.005896136289680304, -0.009237732443554393], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [0.033122670695380566, -0.006422494610347895, 0.0065606284489129465, 0.0023731904660097593, 0.02529645716448017, -0.0015503489953522208, -0.00885813674918088, -0.011680636883665215, 0.003103597931480663, -0.0032887125984264118, -0.003782921451400712, 0.0017666831830142806, -0.054861979732239205, -0.004431203975931452, -0.027944589877879164, -0.02219355694838295, 9.477267471530437e-05, -0.04450201289971312, 0.0017677469556832432, -0.005155080026797168, -0.07743673023784685, -0.04938686537323632, -0.020907190143227356, -0.012034373232683734, -0.00015134441708200552, -0.001182710809050351, -0.003173987684000677, 0.0014795748228695563, 0.0073534900832690365, -0.0008354489267667024, 0.005994501551087671, -0.001949654470300469, -0.025250435448139805, -0.007405976396232998, -0.01364334404516715, -0.006770644820588019, -0.010306546235516472, -0.011511159816227353, -0.014546058678045365, -0.023990360141974858, 0.0021104946413032033, 0.004507719925984088, -0.03336452336797725, 0.00975795712873447, -0.02700246248983946, -0.012851437640422602, -0.006881308811313375, 0.0121542640485479, -0.003466398700888551, -0.0027956197687914984, 0.006550841585410281, -0.014770631204515145, -0.010865934796680894, 0.009154312128834259, -0.001381663756948072, -0.013483048977708967, -0.02183533767161601, -0.02700077518757424, -0.010096966159120185, 0.0006548018896803537, -0.013370796731764865, -0.04856916230201694, 0.003084337112093458, -0.0002001478381022416, -0.018929333253243724, -0.009315709605782468, -0.008477242435249633, -0.008113477059228961, -0.04131690222913414, -0.014994397497922878, -0.01915438574021854, -0.0073147036449580476, -0.001109584190294513, -0.013207670347384314, -0.0032800951696012257, -0.001245808284429193, 0.0006095804852670628, 0.003343324988975929, -0.02460481964367448, 0.0019205916116679794, 0.0049708360816970425, -0.003621874278616497, 0.000340155539984479, -0.0007228877611627701, -0.0031879083954881645, -0.01495513131087924, -0.008615409755051996, -0.010496532884232522, 0.0011720295431539435, -0.00016695054567089813, -0.0023622824669146322, 0.004736660980975294, -0.019945183629219078, -0.0015265639554185983, -9.342410814471708e-05, -0.0060222592335275205, -0.010818014323492617, 0.0013811222678725577, 0.005896136289680304, -0.009237732443554393]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.2491296432823134), 'mean_inference_ms': np.float64(1.9440290739885304), 'mean_action_processing_ms': np.float64(0.0744285265208876), 'mean_env_wait_ms': np.float64(0.10552149633587024), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.0035500526428222656), 'StateBufferConnector_ms': np.float64(0.0021538734436035156), 'ViewRequirementAgentConnector_ms': np.float64(0.06852030754089355)}, 'num_episodes': 40, 'episode_return_max': 0.033122670695380566, 'episode_return_min': -0.07743673023784685, 'episode_return_mean': np.float64(-0.00824668524996413), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 24000, 'num_agent_steps_trained': 24000, 'num_env_steps_sampled': 24000, 'num_env_steps_trained': 24000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 75.7783107389623, 'num_env_steps_trained_throughput_per_sec': 75.7783107389623, 'timesteps_total': 24000, 'num_env_steps_sampled_lifetime': 24000, 'num_agent_steps_sampled_lifetime': 24000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 24000, 'timers': {'training_iteration_time_ms': 53034.685, 'restore_workers_time_ms': 0.041, 'training_step_time_ms': 53034.551, 'sample_time_ms': 5122.168, 'load_time_ms': 3.78, 'load_throughput': 1058143.38, 'learn_time_ms': 47894.273, 'learn_throughput': 83.517, 'synch_weights_time_ms': 13.571}, 'counters': {'num_env_steps_sampled': 24000, 'num_env_steps_trained': 24000, 'num_agent_steps_sampled': 24000, 'num_agent_steps_trained': 24000}, 'done': False, 'training_iteration': 6, 'trial_id': 'default', 'date': '2025-08-05_15-38-03', 'timestamp': 1754379483, 'time_this_iter_s': 52.793023347854614, 'time_total_s': 318.2608914375305, 'pid': 3279764, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 20, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7fed055d02c0>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 318.2608914375305, 'iterations_since_restore': 6, 'perf': {'cpu_util_percent': np.float64(44.41733333333333), 'ram_util_percent': np.float64(53.35066666666667)}})
2025-08-05 15:38:06,188 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 15:39:01,189 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:342 - PPOAgent closed
2025-08-05 15:55:40,362 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 15:55:40,363 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 15:55:40,365 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 15:55:44,972 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 15:55:44,980 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 15:55:50,640 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:55:50,644 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:55:50,648 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:55:50,651 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:55:50,655 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:55:50,659 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 15:55:50,785 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 15:55:50,831 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 15:55:50,832 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 15:55:50,834 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 15:55:58,201 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:55:58,204 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:55:58,207 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:55:58,210 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:55:58,214 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:55:58,217 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 15:55:58,234 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 15:55:58,256 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 15:55:58,258 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 15:55:58,260 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 15:55:58,416 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 15:55:58,417 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 15:56:51,717 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 4000 - Reward: 0.00 - Length: 0.0
2025-08-05 15:57:43,359 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 8000 - Reward: 0.00 - Length: 0.0
2025-08-05 15:57:43,362 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 15:57:43,364 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 15:57:43,367 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:57:43,368 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:57:43,370 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:57:43,371 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:57:43,373 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:57:43,375 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 15:57:46,053 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 15:57:46,055 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: -0.37 ± 0.67
2025-08-05 15:57:46,057 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 50.0
2025-08-05 15:57:46,059 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: -0.37 ± 0.67
2025-08-05 15:57:46,078 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/best_model_step_10000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.3490811642139189), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.014519800858632212), 'policy_loss': np.float64(-0.007424110093063885), 'vf_loss': np.float64(0.006132285602601065), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.008309610266869346), 'entropy': np.float64(1.2654717176191268), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(465.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'env_runners': {'episode_reward_max': 0.04564653193077298, 'episode_reward_min': -0.0873763093287002, 'episode_reward_mean': np.float64(-0.014288385983898384), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 8000, 'policy_reward_min': {'default_policy': np.float64(-0.0873763093287002)}, 'policy_reward_max': {'default_policy': np.float64(0.04564653193077298)}, 'policy_reward_mean': {'default_policy': np.float64(-0.014288385983898384)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009714180754735239, -0.010944758740510596, -0.005046929372566402, -0.005495113304343789, -0.004099135383263067, -0.022455825982407064, -0.02564541502591449, -0.03931054101936092, 0.006260028434742614, -0.029927887829690906, 0.009587621673837112, -0.0030471423258917954, -0.0022913590563493104, -0.0150791473701978, -0.04988864698810995, -0.00601893152902239, 0.04564653193077298, 0.015179787063995658, -0.00427998879028725, -0.017196133050228483, -0.005091830665752554, -0.009948062462730318, 0.025377041508401175, 0.0005859134728660679, -0.008288801766483833, -0.012587936552570837, 0.018495813980957645, -0.010317224741221568, -0.028856403241377965, -0.012766626765768792, -0.031301144270785494, 0.00947748762582115, -0.006710612381727321, -0.012260165344001242, -0.02854446694669119, -0.004893987224412297, -0.03394666373821849, -0.06180961955822188, -0.0034986610278121347, 0.0031191069974325254], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009714180754735239, -0.010944758740510596, -0.005046929372566402, -0.005495113304343789, -0.004099135383263067, -0.022455825982407064, -0.02564541502591449, -0.03931054101936092, 0.006260028434742614, -0.029927887829690906, 0.009587621673837112, -0.0030471423258917954, -0.0022913590563493104, -0.0150791473701978, -0.04988864698810995, -0.00601893152902239, 0.04564653193077298, 0.015179787063995658, -0.00427998879028725, -0.017196133050228483, -0.005091830665752554, -0.009948062462730318, 0.025377041508401175, 0.0005859134728660679, -0.008288801766483833, -0.012587936552570837, 0.018495813980957645, -0.010317224741221568, -0.028856403241377965, -0.012766626765768792, -0.031301144270785494, 0.00947748762582115, -0.006710612381727321, -0.012260165344001242, -0.02854446694669119, -0.004893987224412297, -0.03394666373821849, -0.06180961955822188, -0.0034986610278121347, 0.0031191069974325254]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.26003368204722455), 'mean_inference_ms': np.float64(1.9554412205423877), 'mean_action_processing_ms': np.float64(0.07419776606917153), 'mean_env_wait_ms': np.float64(0.1117842840410646), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.0037050247192382812), 'StateBufferConnector_ms': np.float64(0.0023546814918518066), 'ViewRequirementAgentConnector_ms': np.float64(0.0687512755393982)}, 'num_episodes': 40, 'episode_return_max': 0.04564653193077298, 'episode_return_min': -0.0873763093287002, 'episode_return_mean': np.float64(-0.014288385983898384), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 77.53568452251317, 'num_env_steps_trained_throughput_per_sec': 77.53568452251317, 'timesteps_total': 8000, 'num_env_steps_sampled_lifetime': 8000, 'num_agent_steps_sampled_lifetime': 8000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 8000, 'timers': {'training_iteration_time_ms': 52415.339, 'restore_workers_time_ms': 0.023, 'training_step_time_ms': 52415.252, 'sample_time_ms': 5004.963, 'load_time_ms': 2.561, 'load_throughput': 1561760.856, 'learn_time_ms': 47397.022, 'learn_throughput': 84.393, 'synch_weights_time_ms': 10.036}, 'counters': {'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'done': False, 'training_iteration': 2, 'trial_id': 'default', 'date': '2025-08-05_15-57-43', 'timestamp': 1754380663, 'time_this_iter_s': 51.597524881362915, 'time_total_s': 104.8453598022461, 'pid': 3284759, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 20, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7fa6a75dc180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 104.8453598022461, 'iterations_since_restore': 2, 'perf': {'cpu_util_percent': np.float64(44.01791044776119), 'ram_util_percent': np.float64(53.241791044776114), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.2546268656716418), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.2614925373134328), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 15:57:46,174 - rl_ct.scripts.train - INFO - train.py:125 - New best model saved: checkpoints/quick_start_demo/best_model_step_10000.zip
2025-08-05 15:57:46,192 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_10000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.3490811642139189), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.014519800858632212), 'policy_loss': np.float64(-0.007424110093063885), 'vf_loss': np.float64(0.006132285602601065), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.008309610266869346), 'entropy': np.float64(1.2654717176191268), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(465.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'env_runners': {'episode_reward_max': 0.04564653193077298, 'episode_reward_min': -0.0873763093287002, 'episode_reward_mean': np.float64(-0.014288385983898384), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 8000, 'policy_reward_min': {'default_policy': np.float64(-0.0873763093287002)}, 'policy_reward_max': {'default_policy': np.float64(0.04564653193077298)}, 'policy_reward_mean': {'default_policy': np.float64(-0.014288385983898384)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009714180754735239, -0.010944758740510596, -0.005046929372566402, -0.005495113304343789, -0.004099135383263067, -0.022455825982407064, -0.02564541502591449, -0.03931054101936092, 0.006260028434742614, -0.029927887829690906, 0.009587621673837112, -0.0030471423258917954, -0.0022913590563493104, -0.0150791473701978, -0.04988864698810995, -0.00601893152902239, 0.04564653193077298, 0.015179787063995658, -0.00427998879028725, -0.017196133050228483, -0.005091830665752554, -0.009948062462730318, 0.025377041508401175, 0.0005859134728660679, -0.008288801766483833, -0.012587936552570837, 0.018495813980957645, -0.010317224741221568, -0.028856403241377965, -0.012766626765768792, -0.031301144270785494, 0.00947748762582115, -0.006710612381727321, -0.012260165344001242, -0.02854446694669119, -0.004893987224412297, -0.03394666373821849, -0.06180961955822188, -0.0034986610278121347, 0.0031191069974325254], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009714180754735239, -0.010944758740510596, -0.005046929372566402, -0.005495113304343789, -0.004099135383263067, -0.022455825982407064, -0.02564541502591449, -0.03931054101936092, 0.006260028434742614, -0.029927887829690906, 0.009587621673837112, -0.0030471423258917954, -0.0022913590563493104, -0.0150791473701978, -0.04988864698810995, -0.00601893152902239, 0.04564653193077298, 0.015179787063995658, -0.00427998879028725, -0.017196133050228483, -0.005091830665752554, -0.009948062462730318, 0.025377041508401175, 0.0005859134728660679, -0.008288801766483833, -0.012587936552570837, 0.018495813980957645, -0.010317224741221568, -0.028856403241377965, -0.012766626765768792, -0.031301144270785494, 0.00947748762582115, -0.006710612381727321, -0.012260165344001242, -0.02854446694669119, -0.004893987224412297, -0.03394666373821849, -0.06180961955822188, -0.0034986610278121347, 0.0031191069974325254]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.26003368204722455), 'mean_inference_ms': np.float64(1.9554412205423877), 'mean_action_processing_ms': np.float64(0.07419776606917153), 'mean_env_wait_ms': np.float64(0.1117842840410646), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.0037050247192382812), 'StateBufferConnector_ms': np.float64(0.0023546814918518066), 'ViewRequirementAgentConnector_ms': np.float64(0.0687512755393982)}, 'num_episodes': 40, 'episode_return_max': 0.04564653193077298, 'episode_return_min': -0.0873763093287002, 'episode_return_mean': np.float64(-0.014288385983898384), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 77.53568452251317, 'num_env_steps_trained_throughput_per_sec': 77.53568452251317, 'timesteps_total': 8000, 'num_env_steps_sampled_lifetime': 8000, 'num_agent_steps_sampled_lifetime': 8000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 8000, 'timers': {'training_iteration_time_ms': 52415.339, 'restore_workers_time_ms': 0.023, 'training_step_time_ms': 52415.252, 'sample_time_ms': 5004.963, 'load_time_ms': 2.561, 'load_throughput': 1561760.856, 'learn_time_ms': 47397.022, 'learn_throughput': 84.393, 'synch_weights_time_ms': 10.036}, 'counters': {'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'done': False, 'training_iteration': 2, 'trial_id': 'default', 'date': '2025-08-05_15-57-43', 'timestamp': 1754380663, 'time_this_iter_s': 51.597524881362915, 'time_total_s': 104.8453598022461, 'pid': 3284759, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 20, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7fa6a75dc180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 104.8453598022461, 'iterations_since_restore': 2, 'perf': {'cpu_util_percent': np.float64(44.01791044776119), 'ram_util_percent': np.float64(53.241791044776114), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.2546268656716418), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.2614925373134328), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 15:57:46,276 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 15:58:40,689 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:342 - PPOAgent closed
2025-08-05 15:59:20,523 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 15:59:20,524 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 15:59:20,526 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 15:59:24,720 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 15:59:24,728 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 15:59:30,951 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:59:30,953 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:59:30,956 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:59:30,959 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:59:30,962 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:59:30,966 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 15:59:31,082 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 15:59:31,164 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 15:59:31,167 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 15:59:31,168 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 15:59:38,365 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 15:59:38,368 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 15:59:38,372 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 15:59:38,375 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 15:59:38,378 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 15:59:38,381 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 15:59:38,399 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 15:59:38,425 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 15:59:38,427 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 15:59:38,430 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 15:59:38,480 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 15:59:38,481 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 16:00:31,463 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 4000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:01:10,516 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:342 - PPOAgent closed
2025-08-05 16:01:20,002 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 16:01:20,003 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 16:01:20,004 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 16:01:24,282 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 16:01:24,292 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 16:01:30,696 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:01:30,697 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:01:30,699 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:01:30,701 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:01:30,702 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:01:30,704 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 16:01:30,820 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 16:01:30,863 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 16:01:30,865 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 16:01:30,866 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 16:01:37,593 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:01:37,607 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:01:37,610 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:01:37,614 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:01:37,617 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:01:37,620 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 16:01:37,638 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 16:01:37,655 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 16:01:37,657 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 16:01:37,659 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 16:01:37,711 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 16:01:37,713 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 16:02:29,727 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 4000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:02:47,277 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:342 - PPOAgent closed
2025-08-05 16:03:36,598 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 16:03:36,599 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 16:03:36,600 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 16:03:41,727 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 16:03:41,734 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 16:03:47,713 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:03:47,717 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:03:47,721 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:03:47,725 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:03:47,728 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:03:47,732 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 16:03:47,862 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 16:03:47,917 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 16:03:47,918 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 16:03:47,920 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 16:03:55,659 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:03:55,663 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:03:55,666 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:03:55,669 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:03:55,672 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:03:55,675 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 16:03:55,693 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized TransformerModel on device: cpu
2025-08-05 16:03:55,714 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cpu
2025-08-05 16:03:55,717 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 16:03:55,719 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 16:03:55,773 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 16:03:55,775 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 16:04:01,262 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:342 - PPOAgent closed
2025-08-05 16:05:43,111 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 16:05:43,112 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 16:05:43,114 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 16:05:48,072 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 16:05:48,079 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 16:05:54,082 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:05:54,084 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:05:54,086 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:05:54,087 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:05:54,089 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:05:54,091 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 16:05:54,202 - rl_ct.models.base_model - INFO - base_model.py:62 - Initialized TransformerModel on device: cpu
2025-08-05 16:05:54,245 - rl_ct.models.base_model - INFO - base_model.py:243 - Model moved to device: cpu
2025-08-05 16:05:54,246 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 16:05:54,248 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 16:06:01,852 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:06:01,855 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:06:01,859 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:06:01,862 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:06:01,865 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:06:01,868 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 16:06:01,886 - rl_ct.models.base_model - INFO - base_model.py:62 - Initialized TransformerModel on device: cpu
2025-08-05 16:06:01,904 - rl_ct.models.base_model - INFO - base_model.py:243 - Model moved to device: cpu
2025-08-05 16:06:01,906 - rl_ct.models.transformer - INFO - transformer.py:333 - TransformerModel initialized with 474,501 parameters
2025-08-05 16:06:01,909 - rl_ct.models.rllib_models - INFO - rllib_models.py:94 - RLlibTransformerModel initialized with 474,501 parameters
2025-08-05 16:06:01,960 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:180 - PPO algorithm built
2025-08-05 16:06:01,961 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 16:06:54,877 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 4000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:07:47,079 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 8000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:07:47,082 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 16:07:47,084 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 16:07:47,086 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:07:47,087 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:07:47,089 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:07:47,090 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:07:47,092 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:07:47,094 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 16:07:50,109 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 16:07:50,111 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: 0.00 ± 0.00
2025-08-05 16:07:50,112 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 100.0
2025-08-05 16:07:50,113 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: 0.00 ± 0.00
2025-08-05 16:07:50,133 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/best_model_step_10000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.3293507970148517), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.01202971874811356), 'policy_loss': np.float64(-0.004926569582594018), 'vf_loss': np.float64(0.006060196230218055), 'vf_explained_var': np.float64(-0.9994434245171085), 'kl': np.float64(0.008720984988875927), 'entropy': np.float64(1.2749543532248466), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(465.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'env_runners': {'episode_reward_max': 0.14697580463763033, 'episode_reward_min': -0.0873763093287002, 'episode_reward_mean': np.float64(-0.012406974339881353), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 8000, 'policy_reward_min': {'default_policy': np.float64(-0.0873763093287002)}, 'policy_reward_max': {'default_policy': np.float64(0.14697580463763033)}, 'policy_reward_mean': {'default_policy': np.float64(-0.012406974339881353)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009202270097147056, -0.00804197844231408, -0.007924113705640416, -0.007074824342499758, -0.0006428805668592854, -0.018386870433757453, -0.0218426989743771, -0.033014783903102585, 0.004304714270816988, -0.028777474079597874, 0.007444915217044157, -0.015128189522294952, -0.004221512322471673, -0.0017188808338442912, -0.04845667779268636, -0.0026103632880571485, 0.04249277267741657, 0.002758610967202521, -0.01028685964717819, -0.018978529682404856, -0.00660862714690468, -0.011519333397646164, 0.02106821830804634, 0.007934621041964875, 0.0024107066889899435, 0.14697580463763033, 0.0017820643709224593, -0.0116480868129495, -0.018986834321035338, -0.01214981589422156, -0.00030527111201680834, 0.004188582581964377, -0.009614386672974207, -0.007210813625069383, -0.04100180025013785, -0.004148024364380186, -0.035892118812681825, -0.07045720773335863, -0.007098969041421179, -0.0054318929434343105], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009202270097147056, -0.00804197844231408, -0.007924113705640416, -0.007074824342499758, -0.0006428805668592854, -0.018386870433757453, -0.0218426989743771, -0.033014783903102585, 0.004304714270816988, -0.028777474079597874, 0.007444915217044157, -0.015128189522294952, -0.004221512322471673, -0.0017188808338442912, -0.04845667779268636, -0.0026103632880571485, 0.04249277267741657, 0.002758610967202521, -0.01028685964717819, -0.018978529682404856, -0.00660862714690468, -0.011519333397646164, 0.02106821830804634, 0.007934621041964875, 0.0024107066889899435, 0.14697580463763033, 0.0017820643709224593, -0.0116480868129495, -0.018986834321035338, -0.01214981589422156, -0.00030527111201680834, 0.004188582581964377, -0.009614386672974207, -0.007210813625069383, -0.04100180025013785, -0.004148024364380186, -0.035892118812681825, -0.07045720773335863, -0.007098969041421179, -0.0054318929434343105]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.2366231520304752), 'mean_inference_ms': np.float64(1.7418883077698921), 'mean_action_processing_ms': np.float64(0.06438563369725123), 'mean_env_wait_ms': np.float64(0.09370957481969977), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.0033274292945861816), 'StateBufferConnector_ms': np.float64(0.002103447914123535), 'ViewRequirementAgentConnector_ms': np.float64(0.06140589714050293)}, 'num_episodes': 40, 'episode_return_max': 0.14697580463763033, 'episode_return_min': -0.0873763093287002, 'episode_return_mean': np.float64(-0.012406974339881353), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 76.66914268052518, 'num_env_steps_trained_throughput_per_sec': 76.66914268052518, 'timesteps_total': 8000, 'num_env_steps_sampled_lifetime': 8000, 'num_agent_steps_sampled_lifetime': 8000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 8000, 'timers': {'training_iteration_time_ms': 52521.808, 'restore_workers_time_ms': 0.02, 'training_step_time_ms': 52521.735, 'sample_time_ms': 4510.934, 'load_time_ms': 2.091, 'load_throughput': 1913350.744, 'learn_time_ms': 47998.788, 'learn_throughput': 83.335, 'synch_weights_time_ms': 9.255}, 'counters': {'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'done': False, 'training_iteration': 2, 'trial_id': 'default', 'date': '2025-08-05_16-07-47', 'timestamp': 1754381267, 'time_this_iter_s': 52.179675340652466, 'time_total_s': 105.06414556503296, 'pid': 3294923, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 168, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7f08a92f4180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 105.06414556503296, 'iterations_since_restore': 2, 'perf': {'cpu_util_percent': np.float64(45.74117647058824), 'ram_util_percent': np.float64(53.39117647058824), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.3629411764705882), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.19823529411764707), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 16:07:50,221 - rl_ct.scripts.train - INFO - train.py:125 - New best model saved: checkpoints/quick_start_demo/best_model_step_10000.zip
2025-08-05 16:07:50,238 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_10000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.3293507970148517), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.01202971874811356), 'policy_loss': np.float64(-0.004926569582594018), 'vf_loss': np.float64(0.006060196230218055), 'vf_explained_var': np.float64(-0.9994434245171085), 'kl': np.float64(0.008720984988875927), 'entropy': np.float64(1.2749543532248466), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(465.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'env_runners': {'episode_reward_max': 0.14697580463763033, 'episode_reward_min': -0.0873763093287002, 'episode_reward_mean': np.float64(-0.012406974339881353), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 8000, 'policy_reward_min': {'default_policy': np.float64(-0.0873763093287002)}, 'policy_reward_max': {'default_policy': np.float64(0.14697580463763033)}, 'policy_reward_mean': {'default_policy': np.float64(-0.012406974339881353)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009202270097147056, -0.00804197844231408, -0.007924113705640416, -0.007074824342499758, -0.0006428805668592854, -0.018386870433757453, -0.0218426989743771, -0.033014783903102585, 0.004304714270816988, -0.028777474079597874, 0.007444915217044157, -0.015128189522294952, -0.004221512322471673, -0.0017188808338442912, -0.04845667779268636, -0.0026103632880571485, 0.04249277267741657, 0.002758610967202521, -0.01028685964717819, -0.018978529682404856, -0.00660862714690468, -0.011519333397646164, 0.02106821830804634, 0.007934621041964875, 0.0024107066889899435, 0.14697580463763033, 0.0017820643709224593, -0.0116480868129495, -0.018986834321035338, -0.01214981589422156, -0.00030527111201680834, 0.004188582581964377, -0.009614386672974207, -0.007210813625069383, -0.04100180025013785, -0.004148024364380186, -0.035892118812681825, -0.07045720773335863, -0.007098969041421179, -0.0054318929434343105], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.0873763093287002, -0.0076199311996268115, -0.004192885961182521, -0.02433867349610651, -0.03205791028265674, 0.009495852116976261, -0.06105619465500647, -0.03945872950071328, -0.018310419456079603, -0.03846275859950653, -0.037908045214614466, 0.023892963664396173, 0.012178601534274618, -0.018438724412068393, 0.005190208078218454, -0.016709998085194123, -0.02056978556824471, 0.005568771191583581, -0.01321459129915259, -0.029789458320980608, -0.007066632019170457, -0.0517589783321718, -0.029903327143608972, -0.03278856542347711, 0.017510521178169484, 0.001632335667221681, -0.009885609214035704, -0.016383580964406827, 0.025470533501405473, -0.06752961668861567, 0.0040166417812442234, -0.08526513330799193, 0.0010424633054739816, 0.012230990197991917, 0.021115952919668453, -0.023503590338115138, -0.04231662548589735, 0.006989472459975776, -0.0873004404879162, 0.0013343389985983316, -0.009202270097147056, -0.00804197844231408, -0.007924113705640416, -0.007074824342499758, -0.0006428805668592854, -0.018386870433757453, -0.0218426989743771, -0.033014783903102585, 0.004304714270816988, -0.028777474079597874, 0.007444915217044157, -0.015128189522294952, -0.004221512322471673, -0.0017188808338442912, -0.04845667779268636, -0.0026103632880571485, 0.04249277267741657, 0.002758610967202521, -0.01028685964717819, -0.018978529682404856, -0.00660862714690468, -0.011519333397646164, 0.02106821830804634, 0.007934621041964875, 0.0024107066889899435, 0.14697580463763033, 0.0017820643709224593, -0.0116480868129495, -0.018986834321035338, -0.01214981589422156, -0.00030527111201680834, 0.004188582581964377, -0.009614386672974207, -0.007210813625069383, -0.04100180025013785, -0.004148024364380186, -0.035892118812681825, -0.07045720773335863, -0.007098969041421179, -0.0054318929434343105]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.2366231520304752), 'mean_inference_ms': np.float64(1.7418883077698921), 'mean_action_processing_ms': np.float64(0.06438563369725123), 'mean_env_wait_ms': np.float64(0.09370957481969977), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.0033274292945861816), 'StateBufferConnector_ms': np.float64(0.002103447914123535), 'ViewRequirementAgentConnector_ms': np.float64(0.06140589714050293)}, 'num_episodes': 40, 'episode_return_max': 0.14697580463763033, 'episode_return_min': -0.0873763093287002, 'episode_return_mean': np.float64(-0.012406974339881353), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000, 'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 76.66914268052518, 'num_env_steps_trained_throughput_per_sec': 76.66914268052518, 'timesteps_total': 8000, 'num_env_steps_sampled_lifetime': 8000, 'num_agent_steps_sampled_lifetime': 8000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 8000, 'timers': {'training_iteration_time_ms': 52521.808, 'restore_workers_time_ms': 0.02, 'training_step_time_ms': 52521.735, 'sample_time_ms': 4510.934, 'load_time_ms': 2.091, 'load_throughput': 1913350.744, 'learn_time_ms': 47998.788, 'learn_throughput': 83.335, 'synch_weights_time_ms': 9.255}, 'counters': {'num_env_steps_sampled': 8000, 'num_env_steps_trained': 8000, 'num_agent_steps_sampled': 8000, 'num_agent_steps_trained': 8000}, 'done': False, 'training_iteration': 2, 'trial_id': 'default', 'date': '2025-08-05_16-07-47', 'timestamp': 1754381267, 'time_this_iter_s': 52.179675340652466, 'time_total_s': 105.06414556503296, 'pid': 3294923, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 168, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7f08a92f4180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 105.06414556503296, 'iterations_since_restore': 2, 'perf': {'cpu_util_percent': np.float64(45.74117647058824), 'ram_util_percent': np.float64(53.39117647058824), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.3629411764705882), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.19823529411764707), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 16:07:50,323 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 16:08:43,354 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 12000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:09:35,447 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 16000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:09:35,449 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 16:09:35,451 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 16:09:35,453 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:09:35,454 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:09:35,456 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:09:35,457 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:09:35,459 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:09:35,460 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 16:09:38,357 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 16:09:38,360 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: 0.00 ± 0.00
2025-08-05 16:09:38,361 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 100.0
2025-08-05 16:09:38,362 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: 0.00 ± 0.00
2025-08-05 16:09:38,382 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_20000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.28691483120764455), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.016210413842101493), 'policy_loss': np.float64(-0.007032425935950971), 'vf_loss': np.float64(0.002613719832152128), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.007338787911970529), 'entropy': np.float64(1.2686483494697078), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(1085.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000}, 'env_runners': {'episode_reward_max': 0.14697580463763033, 'episode_reward_min': -0.07045720773335863, 'episode_reward_mean': np.float64(-0.006063098570067881), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 10000, 'policy_reward_min': {'default_policy': np.float64(-0.07045720773335863)}, 'policy_reward_max': {'default_policy': np.float64(0.14697580463763033)}, 'policy_reward_mean': {'default_policy': np.float64(-0.006063098570067881)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.00660862714690468, -0.011519333397646164, 0.02106821830804634, 0.007934621041964875, 0.0024107066889899435, 0.14697580463763033, 0.0017820643709224593, -0.0116480868129495, -0.018986834321035338, -0.01214981589422156, -0.00030527111201680834, 0.004188582581964377, -0.009614386672974207, -0.007210813625069383, -0.04100180025013785, -0.004148024364380186, -0.035892118812681825, -0.07045720773335863, -0.007098969041421179, -0.0054318929434343105, -0.0609957494058499, -0.009599851134197281, -0.003452969602733419, -0.0039631598444634306, -0.010173233326724191, -0.0036803027727509412, -0.026299938833422926, -0.024592120036036856, -0.00413479268901629, -0.007312916384451607, 0.004116226220777605, 0.006725982229379696, -0.004144368425545414, -0.012536501722147667, 0.003485808006492005, -0.03587053250575902, -0.009003040627114253, -0.014638493620300006, 0.003781164473342282, -0.011640487158794816, -0.01436510717549821, -0.0022072193732609983, -0.007870728376931702, -0.0028467267725443817, -0.01797271372907238, 0.0031642925521391356, -0.0006637825327330347, 0.0008689416358997615, -0.010859134164281773, 0.0047035296269867626, -0.009543672193324575, -0.014200449263094711, -0.006070419480256743, -0.002242405849123691, 0.01302186262832258, -0.00636877959982662, 0.012277246779658014, 0.010096338643550628, -0.004991384901468398, 0.014217803479341285, -0.023084823618985575, -0.010178867905640815, 0.009292971582405506, -0.0323814184598347, -0.015947711359329382, -0.00043562966764118874, -0.0011565301470901537, 0.008897246185927748, 0.009424182538173842, 0.007071389753207796, -0.0011849808747088925, -0.020918426412049076, -0.013546658952657517, -0.010222393700974887, -0.01839469625840185, 0.0005409908571994484, 0.004046742430110745, -0.0002302968786809679, -0.019971687174269066, 0.0018684718606821506, 0.01641276025175096, -0.010012486382075268, 0.006249044171322159, 0.004984693891728701, 0.014332120535932589, -0.002042074515332521, -0.009164271514920386, -0.010847391782598966, 0.0036986897484219197, -0.0035843415933945535, -0.004368961607420124, -0.003550965573971979, -0.05484325431594758, -0.008408770744764914, -0.02974841827485162, -0.02287672012506193, -0.00021639469852733316, -0.04391123080593749, 0.001992063263109108, -0.00039685100814326746], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.00660862714690468, -0.011519333397646164, 0.02106821830804634, 0.007934621041964875, 0.0024107066889899435, 0.14697580463763033, 0.0017820643709224593, -0.0116480868129495, -0.018986834321035338, -0.01214981589422156, -0.00030527111201680834, 0.004188582581964377, -0.009614386672974207, -0.007210813625069383, -0.04100180025013785, -0.004148024364380186, -0.035892118812681825, -0.07045720773335863, -0.007098969041421179, -0.0054318929434343105, -0.0609957494058499, -0.009599851134197281, -0.003452969602733419, -0.0039631598444634306, -0.010173233326724191, -0.0036803027727509412, -0.026299938833422926, -0.024592120036036856, -0.00413479268901629, -0.007312916384451607, 0.004116226220777605, 0.006725982229379696, -0.004144368425545414, -0.012536501722147667, 0.003485808006492005, -0.03587053250575902, -0.009003040627114253, -0.014638493620300006, 0.003781164473342282, -0.011640487158794816, -0.01436510717549821, -0.0022072193732609983, -0.007870728376931702, -0.0028467267725443817, -0.01797271372907238, 0.0031642925521391356, -0.0006637825327330347, 0.0008689416358997615, -0.010859134164281773, 0.0047035296269867626, -0.009543672193324575, -0.014200449263094711, -0.006070419480256743, -0.002242405849123691, 0.01302186262832258, -0.00636877959982662, 0.012277246779658014, 0.010096338643550628, -0.004991384901468398, 0.014217803479341285, -0.023084823618985575, -0.010178867905640815, 0.009292971582405506, -0.0323814184598347, -0.015947711359329382, -0.00043562966764118874, -0.0011565301470901537, 0.008897246185927748, 0.009424182538173842, 0.007071389753207796, -0.0011849808747088925, -0.020918426412049076, -0.013546658952657517, -0.010222393700974887, -0.01839469625840185, 0.0005409908571994484, 0.004046742430110745, -0.0002302968786809679, -0.019971687174269066, 0.0018684718606821506, 0.01641276025175096, -0.010012486382075268, 0.006249044171322159, 0.004984693891728701, 0.014332120535932589, -0.002042074515332521, -0.009164271514920386, -0.010847391782598966, 0.0036986897484219197, -0.0035843415933945535, -0.004368961607420124, -0.003550965573971979, -0.05484325431594758, -0.008408770744764914, -0.02974841827485162, -0.02287672012506193, -0.00021639469852733316, -0.04391123080593749, 0.001992063263109108, -0.00039685100814326746]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.23974895457437775), 'mean_inference_ms': np.float64(1.7591087653337296), 'mean_action_processing_ms': np.float64(0.06491275816060527), 'mean_env_wait_ms': np.float64(0.09559750496218075), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.002153635025024414), 'StateBufferConnector_ms': np.float64(0.002084970474243164), 'ViewRequirementAgentConnector_ms': np.float64(0.06234478950500488)}, 'num_episodes': 40, 'episode_return_max': 0.14697580463763033, 'episode_return_min': -0.07045720773335863, 'episode_return_mean': np.float64(-0.006063098570067881), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000, 'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 76.83649381726765, 'num_env_steps_trained_throughput_per_sec': 76.83649381726765, 'timesteps_total': 16000, 'num_env_steps_sampled_lifetime': 16000, 'num_agent_steps_sampled_lifetime': 16000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 16000, 'timers': {'training_iteration_time_ms': 52524.409, 'restore_workers_time_ms': 0.024, 'training_step_time_ms': 52524.312, 'sample_time_ms': 4571.007, 'load_time_ms': 3.573, 'load_throughput': 1119413.912, 'learn_time_ms': 47940.132, 'learn_throughput': 83.437, 'synch_weights_time_ms': 8.937}, 'counters': {'num_env_steps_sampled': 16000, 'num_env_steps_trained': 16000, 'num_agent_steps_sampled': 16000, 'num_agent_steps_trained': 16000}, 'done': False, 'training_iteration': 4, 'trial_id': 'default', 'date': '2025-08-05_16-09-35', 'timestamp': 1754381375, 'time_this_iter_s': 52.067309617996216, 'time_total_s': 210.13551545143127, 'pid': 3294923, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 168, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7f08a92f4180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 210.13551545143127, 'iterations_since_restore': 4, 'perf': {'cpu_util_percent': np.float64(45.471641791044775), 'ram_util_percent': np.float64(54.24029850746268), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.2461194029850746), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.2859701492537313), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 16:09:38,458 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 16:10:30,709 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 20000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:11:24,079 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 24000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:11:24,081 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 16:11:24,083 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 16:11:24,086 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:11:24,087 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:11:24,089 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:11:24,091 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:11:24,093 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:11:24,095 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 16:11:26,873 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 16:11:26,875 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: 0.00 ± 0.00
2025-08-05 16:11:26,876 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 100.0
2025-08-05 16:11:26,878 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: 0.00 ± 0.00
2025-08-05 16:11:26,896 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_30000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.23224488249228847), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.023221910934174254), 'policy_loss': np.float64(-0.014006971706065439), 'vf_loss': np.float64(0.0015482766963090867), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.007844503286610087), 'entropy': np.float64(1.2342429303353832), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(1705.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 24000, 'num_env_steps_trained': 24000, 'num_agent_steps_sampled': 24000, 'num_agent_steps_trained': 24000}, 'env_runners': {'episode_reward_max': 0.01641276025175096, 'episode_reward_min': -0.05484325431594758, 'episode_reward_mean': np.float64(-0.008270210780267035), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 10000, 'policy_reward_min': {'default_policy': np.float64(-0.05484325431594758)}, 'policy_reward_max': {'default_policy': np.float64(0.01641276025175096)}, 'policy_reward_mean': {'default_policy': np.float64(-0.008270210780267035)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [0.01641276025175096, -0.010012486382075268, 0.006249044171322159, 0.004984693891728701, 0.014332120535932589, -0.002042074515332521, -0.009164271514920386, -0.010847391782598966, 0.0036986897484219197, -0.0035843415933945535, -0.004368961607420124, -0.003550965573971979, -0.05484325431594758, -0.008408770744764914, -0.02974841827485162, -0.02287672012506193, -0.00021639469852733316, -0.04391123080593749, 0.001992063263109108, -0.00039685100814326746, -0.04832524804781481, -0.035966320617312655, -0.021700857788117645, -0.020996187983011057, -0.0019636286807221265, -0.0010556124811406647, -0.00254629478217557, 3.6411774876949146e-05, 0.005331943951194103, 0.002069830468605125, -0.004063127745938664, -0.0016170816957707564, -0.024970299867132662, -0.0042942219399960905, -0.01482483029933667, -0.005756648505264335, -0.0035243849724833553, -0.011512878903993561, -0.014978676861380526, 0.004071532923546572, 0.0007233182509994562, 0.0066786286478152206, -0.0542093922298951, 0.0009342102819815682, -0.024653597037894676, -0.004168192582153096, -0.007900226769986793, 0.0121542640485479, -0.0029651481074686355, -0.0020197171790596307, 0.00797447043042355, -0.015964560406200663, -0.00893558489230603, 0.008644739995714674, 0.0023527204585742263, -0.012186118182416598, -0.023574207388123554, -0.017568230609907483, -0.010428912272746518, 0.00046946727809940234, -0.01877341025231242, -0.048317094384298265, 0.0025569165825422204, -0.002030371746666617, -0.018315062987880576, -0.006929597690176033, -0.010534884907973609, -0.006998172992999474, -0.041068588573011995, -0.015554484995370925, -0.018964420860163398, -0.00613549715177282, -0.02421298464652696, -0.01332011508514759, -0.004317041765321735, -0.0006058551708063055, 0.00046367953870969127, 0.0030070568958999854, -0.024308287668716066, 0.0026351055196174103, 0.0008569836247670042, -0.003621874278616497, 0.002795141002240202, 0.0026230627195622594, -0.0009611209587355521, -0.0021010085133903018, -0.016870245766699668, -0.010639941735576591, 0.001250376508363505, -0.0020315731179194347, -0.003617228805052289, 0.0015731211949661962, -0.020588272932707798, -0.001970798323396269, 0.00404174257036133, -0.007815369627384185, -0.008286431938309568, 0.002448987899269471, 0.005167255273009035, -0.010019359079025202], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [0.01641276025175096, -0.010012486382075268, 0.006249044171322159, 0.004984693891728701, 0.014332120535932589, -0.002042074515332521, -0.009164271514920386, -0.010847391782598966, 0.0036986897484219197, -0.0035843415933945535, -0.004368961607420124, -0.003550965573971979, -0.05484325431594758, -0.008408770744764914, -0.02974841827485162, -0.02287672012506193, -0.00021639469852733316, -0.04391123080593749, 0.001992063263109108, -0.00039685100814326746, -0.04832524804781481, -0.035966320617312655, -0.021700857788117645, -0.020996187983011057, -0.0019636286807221265, -0.0010556124811406647, -0.00254629478217557, 3.6411774876949146e-05, 0.005331943951194103, 0.002069830468605125, -0.004063127745938664, -0.0016170816957707564, -0.024970299867132662, -0.0042942219399960905, -0.01482483029933667, -0.005756648505264335, -0.0035243849724833553, -0.011512878903993561, -0.014978676861380526, 0.004071532923546572, 0.0007233182509994562, 0.0066786286478152206, -0.0542093922298951, 0.0009342102819815682, -0.024653597037894676, -0.004168192582153096, -0.007900226769986793, 0.0121542640485479, -0.0029651481074686355, -0.0020197171790596307, 0.00797447043042355, -0.015964560406200663, -0.00893558489230603, 0.008644739995714674, 0.0023527204585742263, -0.012186118182416598, -0.023574207388123554, -0.017568230609907483, -0.010428912272746518, 0.00046946727809940234, -0.01877341025231242, -0.048317094384298265, 0.0025569165825422204, -0.002030371746666617, -0.018315062987880576, -0.006929597690176033, -0.010534884907973609, -0.006998172992999474, -0.041068588573011995, -0.015554484995370925, -0.018964420860163398, -0.00613549715177282, -0.02421298464652696, -0.01332011508514759, -0.004317041765321735, -0.0006058551708063055, 0.00046367953870969127, 0.0030070568958999854, -0.024308287668716066, 0.0026351055196174103, 0.0008569836247670042, -0.003621874278616497, 0.002795141002240202, 0.0026230627195622594, -0.0009611209587355521, -0.0021010085133903018, -0.016870245766699668, -0.010639941735576591, 0.001250376508363505, -0.0020315731179194347, -0.003617228805052289, 0.0015731211949661962, -0.020588272932707798, -0.001970798323396269, 0.00404174257036133, -0.007815369627384185, -0.008286431938309568, 0.002448987899269471, 0.005167255273009035, -0.010019359079025202]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.24202418014084517), 'mean_inference_ms': np.float64(1.7684079538122983), 'mean_action_processing_ms': np.float64(0.06547861259411558), 'mean_env_wait_ms': np.float64(0.09670372162820398), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.0032989978790283203), 'StateBufferConnector_ms': np.float64(0.0021080970764160156), 'ViewRequirementAgentConnector_ms': np.float64(0.06250429153442383)}, 'num_episodes': 40, 'episode_return_max': 0.01641276025175096, 'episode_return_min': -0.05484325431594758, 'episode_return_mean': np.float64(-0.008270210780267035), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 24000, 'num_agent_steps_trained': 24000, 'num_env_steps_sampled': 24000, 'num_env_steps_trained': 24000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 75.00688295642469, 'num_env_steps_trained_throughput_per_sec': 75.00688295642469, 'timesteps_total': 24000, 'num_env_steps_sampled_lifetime': 24000, 'num_agent_steps_sampled_lifetime': 24000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 24000, 'timers': {'training_iteration_time_ms': 52596.422, 'restore_workers_time_ms': 0.031, 'training_step_time_ms': 52596.316, 'sample_time_ms': 4651.222, 'load_time_ms': 3.501, 'load_throughput': 1142395.206, 'learn_time_ms': 47928.737, 'learn_throughput': 83.457, 'synch_weights_time_ms': 12.19}, 'counters': {'num_env_steps_sampled': 24000, 'num_env_steps_trained': 24000, 'num_agent_steps_sampled': 24000, 'num_agent_steps_trained': 24000}, 'done': False, 'training_iteration': 6, 'trial_id': 'default', 'date': '2025-08-05_16-11-24', 'timestamp': 1754381484, 'time_this_iter_s': 53.34018421173096, 'time_total_s': 315.63613057136536, 'pid': 3294923, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 168, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7f08a92f4180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 315.63613057136536, 'iterations_since_restore': 6, 'perf': {'cpu_util_percent': np.float64(46.01176470588234), 'ram_util_percent': np.float64(54.28823529411766), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.3157352941176471), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.2885294117647058), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 16:11:26,974 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 16:12:19,611 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 28000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:13:11,918 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 32000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:13:11,921 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 16:13:11,922 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 16:13:11,925 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:13:11,926 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:13:11,928 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:13:11,929 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:13:11,931 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:13:11,933 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 16:13:15,491 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 16:13:15,494 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: 0.00 ± 0.00
2025-08-05 16:13:15,495 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 100.0
2025-08-05 16:13:15,496 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: 0.00 ± 0.00
2025-08-05 16:13:15,511 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_40000.zip), metrics={'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.2024614459564609), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.023177976696752013), 'policy_loss': np.float64(-0.014621417231166795), 'vf_loss': np.float64(0.0010128898833382635), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.00761150500864812), 'entropy': np.float64(1.1346455397144442), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(2325.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 32000, 'num_env_steps_trained': 32000, 'num_agent_steps_sampled': 32000, 'num_agent_steps_trained': 32000}, 'env_runners': {'episode_reward_max': 0.012254037020126113, 'episode_reward_min': -0.028660277603710392, 'episode_reward_mean': np.float64(-0.004521390004263666), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 10000, 'policy_reward_min': {'default_policy': np.float64(-0.028660277603710392)}, 'policy_reward_max': {'default_policy': np.float64(0.012254037020126113)}, 'policy_reward_mean': {'default_policy': np.float64(-0.004521390004263666)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [0.0008569836247670042, -0.003621874278616497, 0.002795141002240202, 0.0026230627195622594, -0.0009611209587355521, -0.0021010085133903018, -0.016870245766699668, -0.010639941735576591, 0.001250376508363505, -0.0020315731179194347, -0.003617228805052289, 0.0015731211949661962, -0.020588272932707798, -0.001970798323396269, 0.00404174257036133, -0.007815369627384185, -0.008286431938309568, 0.002448987899269471, 0.005167255273009035, -0.010019359079025202, 0.0021716051581128024, -0.001040883606157634, 0.0006967438432961617, -0.019793453561947488, -0.0008834897431992794, -0.0002705348347078265, -0.00925760375673601, 0.012254037020126113, -0.023578096358869923, -0.01586524087822666, -0.00537799149792378, -0.016508697999557922, 0.0006687628558355169, -0.024437789239604997, 0.007270468723793114, -0.000585786261724671, -0.0031319467530406767, -0.0006929958045451337, 0.0008047729517531681, -0.012589401684759817, -0.006373166357863603, -0.0016359174358286203, 0.005631153180825611, -0.002295482153475654, -0.0055123305338591975, -0.013461295653771497, -0.014063895344003412, -0.006124422880624892, -0.0012544119629112963, -0.02146285411006751, 0.003809441505061833, 0.004565777695525224, -0.014915981220911766, -0.005147211151628127, -0.015293915619376331, 0.0007869411246840419, -0.028660277603710392, -0.015828649300312914, -0.004733283465919379, 0.0016736956321967127, -0.011011483663341285, 0.006126773411861761, -0.001155670483850603, 0.0013947874552572449, -0.0012956523891934461, -0.003078667784896908, -0.025351344268887965, 0.0008678628695151826, 0.0005874836102540085, -0.00042430800344655205, 0.0020250595139849475, -0.0011805186653503254, 0.0009219410716490208, -0.004578279934586574, -0.01103664187336055, -0.0016705480735908843, -0.00333346427281272, -0.0033041679928516345, -0.009087292663881174, -0.00561380855305765, -0.001139724436989448, -0.0021627484058214432, 0.003986554964156678, -0.0034899928055567367, 0.005816978988381209, 0.0014700477908321727, -0.0010211663761216724, -0.013882771964191973, -0.002407527388216802, -0.0021084717933117017, -0.010113166572809662, -0.011808421939538224, -0.00933585707818611, 0.005469459171603622, -0.009171670937009093, 0.0017863290430878698, 0.00035217265626385705, -0.0010854449593693943, -0.00011778598727345962, -0.014767690337379657], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [0.0008569836247670042, -0.003621874278616497, 0.002795141002240202, 0.0026230627195622594, -0.0009611209587355521, -0.0021010085133903018, -0.016870245766699668, -0.010639941735576591, 0.001250376508363505, -0.0020315731179194347, -0.003617228805052289, 0.0015731211949661962, -0.020588272932707798, -0.001970798323396269, 0.00404174257036133, -0.007815369627384185, -0.008286431938309568, 0.002448987899269471, 0.005167255273009035, -0.010019359079025202, 0.0021716051581128024, -0.001040883606157634, 0.0006967438432961617, -0.019793453561947488, -0.0008834897431992794, -0.0002705348347078265, -0.00925760375673601, 0.012254037020126113, -0.023578096358869923, -0.01586524087822666, -0.00537799149792378, -0.016508697999557922, 0.0006687628558355169, -0.024437789239604997, 0.007270468723793114, -0.000585786261724671, -0.0031319467530406767, -0.0006929958045451337, 0.0008047729517531681, -0.012589401684759817, -0.006373166357863603, -0.0016359174358286203, 0.005631153180825611, -0.002295482153475654, -0.0055123305338591975, -0.013461295653771497, -0.014063895344003412, -0.006124422880624892, -0.0012544119629112963, -0.02146285411006751, 0.003809441505061833, 0.004565777695525224, -0.014915981220911766, -0.005147211151628127, -0.015293915619376331, 0.0007869411246840419, -0.028660277603710392, -0.015828649300312914, -0.004733283465919379, 0.0016736956321967127, -0.011011483663341285, 0.006126773411861761, -0.001155670483850603, 0.0013947874552572449, -0.0012956523891934461, -0.003078667784896908, -0.025351344268887965, 0.0008678628695151826, 0.0005874836102540085, -0.00042430800344655205, 0.0020250595139849475, -0.0011805186653503254, 0.0009219410716490208, -0.004578279934586574, -0.01103664187336055, -0.0016705480735908843, -0.00333346427281272, -0.0033041679928516345, -0.009087292663881174, -0.00561380855305765, -0.001139724436989448, -0.0021627484058214432, 0.003986554964156678, -0.0034899928055567367, 0.005816978988381209, 0.0014700477908321727, -0.0010211663761216724, -0.013882771964191973, -0.002407527388216802, -0.0021084717933117017, -0.010113166572809662, -0.011808421939538224, -0.00933585707818611, 0.005469459171603622, -0.009171670937009093, 0.0017863290430878698, 0.00035217265626385705, -0.0010854449593693943, -0.00011778598727345962, -0.014767690337379657]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.24418823339893586), 'mean_inference_ms': np.float64(1.7854496125870236), 'mean_action_processing_ms': np.float64(0.06628217464287516), 'mean_env_wait_ms': np.float64(0.09860353080640975), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.002764463424682617), 'StateBufferConnector_ms': np.float64(0.002147674560546875), 'ViewRequirementAgentConnector_ms': np.float64(0.06761813163757324)}, 'num_episodes': 40, 'episode_return_max': 0.012254037020126113, 'episode_return_min': -0.028660277603710392, 'episode_return_mean': np.float64(-0.004521390004263666), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 32000, 'num_agent_steps_trained': 32000, 'num_env_steps_sampled': 32000, 'num_env_steps_trained': 32000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 76.51902563130416, 'num_env_steps_trained_throughput_per_sec': 76.51902563130416, 'timesteps_total': 32000, 'num_env_steps_sampled_lifetime': 32000, 'num_agent_steps_sampled_lifetime': 32000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 32000, 'timers': {'training_iteration_time_ms': 52557.628, 'restore_workers_time_ms': 0.028, 'training_step_time_ms': 52557.533, 'sample_time_ms': 4682.222, 'load_time_ms': 3.617, 'load_throughput': 1105792.102, 'learn_time_ms': 47859.699, 'learn_throughput': 83.578, 'synch_weights_time_ms': 11.371}, 'counters': {'num_env_steps_sampled': 32000, 'num_env_steps_trained': 32000, 'num_agent_steps_sampled': 32000, 'num_agent_steps_trained': 32000}, 'done': False, 'training_iteration': 8, 'trial_id': 'default', 'date': '2025-08-05_16-13-11', 'timestamp': 1754381591, 'time_this_iter_s': 52.28194236755371, 'time_total_s': 420.5340082645416, 'pid': 3294923, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 168, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7f08a92f4180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 420.5340082645416, 'iterations_since_restore': 8, 'perf': {'cpu_util_percent': np.float64(46.48676470588235), 'ram_util_percent': np.float64(53.848529411764694), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.3714705882352941), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.28691176470588237), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 16:13:15,591 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:248 - Starting training for 2 iterations (10000 timesteps)
2025-08-05 16:14:09,142 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 1/2 - Steps: 36000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:15:02,226 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:263 - Iteration 2/2 - Steps: 40000 - Reward: 0.00 - Length: 0.0
2025-08-05 16:15:02,231 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:274 - Training completed
2025-08-05 16:15:02,234 - rl_ct.scripts.train - INFO - train.py:103 - Running evaluation...
2025-08-05 16:15:02,240 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 16:15:02,243 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 16:15:02,247 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 16:15:02,250 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 16:15:02,254 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 16:15:02,257 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: evaluation
2025-08-05 16:15:05,355 - rl_ct.agents.base_agent - INFO - base_agent.py:225 - Evaluation over 10 episodes:
2025-08-05 16:15:05,358 - rl_ct.agents.base_agent - INFO - base_agent.py:226 - Mean reward: 0.00 ± 0.00
2025-08-05 16:15:05,359 - rl_ct.agents.base_agent - INFO - base_agent.py:227 - Mean episode length: 100.0
2025-08-05 16:15:05,360 - rl_ct.scripts.train - INFO - train.py:118 - Evaluation - Mean reward: 0.00 ± 0.00
2025-08-05 16:15:05,379 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/model_step_50000.zip), metrics={'evaluation': {'env_runners': {'episode_reward_max': 0.0, 'episode_reward_min': 0.0, 'episode_reward_mean': np.float64(0.0), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 1000, 'policy_reward_min': {'default_policy': np.float64(0.0)}, 'policy_reward_max': {'default_policy': np.float64(0.0)}, 'policy_reward_mean': {'default_policy': np.float64(0.0)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.2563342626569026), 'mean_inference_ms': np.float64(1.9697609169366008), 'mean_action_processing_ms': np.float64(0.07722510593270979), 'mean_env_wait_ms': np.float64(0.12046290137579843), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.00732421875), 'StateBufferConnector_ms': np.float64(0.004405975341796875), 'ViewRequirementAgentConnector_ms': np.float64(0.06720304489135742)}, 'num_episodes': 10, 'episode_return_max': 0.0, 'episode_return_min': 0.0, 'episode_return_mean': np.float64(0.0), 'episodes_this_iter': 10}, 'num_agent_steps_sampled_this_iter': 1000, 'num_env_steps_sampled_this_iter': 1000, 'timesteps_this_iter': 1000, 'num_healthy_workers': 1, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0}, 'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.20293236732963593), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.013446552103625671), 'policy_loss': np.float64(-0.0054155447310017), 'vf_loss': np.float64(0.0007048519046586607), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.004940095240054953), 'entropy': np.float64(0.986546342603622), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(2945.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 40000, 'num_env_steps_trained': 40000, 'num_agent_steps_sampled': 40000, 'num_agent_steps_trained': 40000, 'num_env_steps_sampled_for_evaluation_this_iter': 1000}, 'env_runners': {'episode_reward_max': 0.005816978988381209, 'episode_reward_min': -0.022700817197977695, 'episode_reward_mean': np.float64(-0.002893244923410918), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 10000, 'policy_reward_min': {'default_policy': np.float64(-0.022700817197977695)}, 'policy_reward_max': {'default_policy': np.float64(0.005816978988381209)}, 'policy_reward_mean': {'default_policy': np.float64(-0.002893244923410918)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.001139724436989448, -0.0021627484058214432, 0.003986554964156678, -0.0034899928055567367, 0.005816978988381209, 0.0014700477908321727, -0.0010211663761216724, -0.013882771964191973, -0.002407527388216802, -0.0021084717933117017, -0.010113166572809662, -0.011808421939538224, -0.00933585707818611, 0.005469459171603622, -0.009171670937009093, 0.0017863290430878698, 0.00035217265626385705, -0.0010854449593693943, -0.00011778598727345962, -0.014767690337379657, -0.00235831504355335, 0.00011829303198250887, 0.0003014322504954984, -0.00048006091610986595, 0.0015997125931317049, 0.001578278194510294, -0.005467624537765704, -0.000930691036056806, -0.013659533821885634, -0.020052121952178523, 0.0002903284573682937, 0.0024304025407495544, -0.01280405232192328, -0.0007193378162399632, -0.0029818607956436823, -0.000511229795241399, -0.0018433330559831807, -0.00791847921527569, 0.005717334566185489, 0.0004188914410341374, -0.004087515200671944, 0.0007460806750764562, -0.0014861041798936454, -0.0010816488917675515, -0.0012011567107797667, -0.006869472497056892, -0.006584832700714376, -0.0024135298379889415, -0.004370188227677759, 0.0003185213129546123, -0.004602008271818095, 0.0038331546670813278, -0.0005265921771274058, -0.0005479701603566985, 0.002315564391588869, -0.011306938174855306, -0.011893284206734219, 0.00019058992391489914, -0.008820770227449365, -0.0027528277187319414, 0.0005218281638917893, 0.0014665834698050287, -0.002111203809965172, 0.0024002928387318306, -0.0016726618669982707, -0.0008898012123015955, -0.0022215450898659377, -0.005117079524632435, 0.0013187318412409051, -0.022700817197977695, -0.008885856170372106, -0.0016519282352903796, -0.0002899214602968027, -0.0009733207385233152, -0.0020310288815470773, -0.00020170156563757355, -0.0008929229186882045, 0.0021170191880359665, -0.0009719180142277149, -8.901413267187706e-05, -0.00019512688021090102, -0.0013451515515707675, -0.003673360763820823, 0.002289563209659066, 0.0034557776650281846, -0.0014316514062059013, 0.0006810471275574704, -0.010053547089292403, 0.0004125136818990925, -0.013957565063073845, -0.00154964098829323, -0.0012913488673214173, -0.009213229663003876, -0.0024973588302350403, -0.00991351696420288, -0.009011280330383706, 0.002912949982728631, -0.010666483492910656, -0.0003417301159248879, 0.0010847071286340503], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.001139724436989448, -0.0021627484058214432, 0.003986554964156678, -0.0034899928055567367, 0.005816978988381209, 0.0014700477908321727, -0.0010211663761216724, -0.013882771964191973, -0.002407527388216802, -0.0021084717933117017, -0.010113166572809662, -0.011808421939538224, -0.00933585707818611, 0.005469459171603622, -0.009171670937009093, 0.0017863290430878698, 0.00035217265626385705, -0.0010854449593693943, -0.00011778598727345962, -0.014767690337379657, -0.00235831504355335, 0.00011829303198250887, 0.0003014322504954984, -0.00048006091610986595, 0.0015997125931317049, 0.001578278194510294, -0.005467624537765704, -0.000930691036056806, -0.013659533821885634, -0.020052121952178523, 0.0002903284573682937, 0.0024304025407495544, -0.01280405232192328, -0.0007193378162399632, -0.0029818607956436823, -0.000511229795241399, -0.0018433330559831807, -0.00791847921527569, 0.005717334566185489, 0.0004188914410341374, -0.004087515200671944, 0.0007460806750764562, -0.0014861041798936454, -0.0010816488917675515, -0.0012011567107797667, -0.006869472497056892, -0.006584832700714376, -0.0024135298379889415, -0.004370188227677759, 0.0003185213129546123, -0.004602008271818095, 0.0038331546670813278, -0.0005265921771274058, -0.0005479701603566985, 0.002315564391588869, -0.011306938174855306, -0.011893284206734219, 0.00019058992391489914, -0.008820770227449365, -0.0027528277187319414, 0.0005218281638917893, 0.0014665834698050287, -0.002111203809965172, 0.0024002928387318306, -0.0016726618669982707, -0.0008898012123015955, -0.0022215450898659377, -0.005117079524632435, 0.0013187318412409051, -0.022700817197977695, -0.008885856170372106, -0.0016519282352903796, -0.0002899214602968027, -0.0009733207385233152, -0.0020310288815470773, -0.00020170156563757355, -0.0008929229186882045, 0.0021170191880359665, -0.0009719180142277149, -8.901413267187706e-05, -0.00019512688021090102, -0.0013451515515707675, -0.003673360763820823, 0.002289563209659066, 0.0034557776650281846, -0.0014316514062059013, 0.0006810471275574704, -0.010053547089292403, 0.0004125136818990925, -0.013957565063073845, -0.00154964098829323, -0.0012913488673214173, -0.009213229663003876, -0.0024973588302350403, -0.00991351696420288, -0.009011280330383706, 0.002912949982728631, -0.010666483492910656, -0.0003417301159248879, 0.0010847071286340503]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.24527072259159183), 'mean_inference_ms': np.float64(1.7967967404149363), 'mean_action_processing_ms': np.float64(0.0668114490877102), 'mean_env_wait_ms': np.float64(0.09970113153504069), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.002383708953857422), 'StateBufferConnector_ms': np.float64(0.0022521018981933594), 'ViewRequirementAgentConnector_ms': np.float64(0.06951642036437988)}, 'num_episodes': 40, 'episode_return_max': 0.005816978988381209, 'episode_return_min': -0.022700817197977695, 'episode_return_mean': np.float64(-0.002893244923410918), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 40000, 'num_agent_steps_trained': 40000, 'num_env_steps_sampled': 40000, 'num_env_steps_trained': 40000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 75.42751580762028, 'num_env_steps_trained_throughput_per_sec': 75.42751580762028, 'timesteps_total': 40000, 'num_env_steps_sampled_lifetime': 40000, 'num_agent_steps_sampled_lifetime': 40000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 40000, 'timers': {'training_iteration_time_ms': 52701.114, 'restore_workers_time_ms': 0.026, 'training_step_time_ms': 52701.022, 'sample_time_ms': 4688.309, 'load_time_ms': 4.084, 'load_throughput': 979474.102, 'learn_time_ms': 47997.048, 'learn_throughput': 83.338, 'synch_weights_time_ms': 10.972, 'restore_eval_workers_time_ms': 0.024, 'evaluation_iteration_time_ms': 2459.373, 'evaluation_iteration_throughput': 406.608}, 'counters': {'num_env_steps_sampled': 40000, 'num_env_steps_trained': 40000, 'num_agent_steps_sampled': 40000, 'num_agent_steps_trained': 40000, 'num_env_steps_sampled_for_evaluation_this_iter': 1000}, 'done': False, 'training_iteration': 10, 'trial_id': 'default', 'date': '2025-08-05_16-15-02', 'timestamp': 1754381702, 'time_this_iter_s': 53.04372024536133, 'time_total_s': 527.1085984706879, 'pid': 3294923, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 168, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7f08a92f4180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 527.1085984706879, 'iterations_since_restore': 10, 'perf': {'cpu_util_percent': np.float64(45.93970588235294), 'ram_util_percent': np.float64(53.8220588235294), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.3394117647058823), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.2291176470588235), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 16:15:05,475 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:292 - Agent saved to TrainingResult(checkpoint=Checkpoint(filesystem=local, path=checkpoints/quick_start_demo/final_model.zip), metrics={'evaluation': {'env_runners': {'episode_reward_max': 0.0, 'episode_reward_min': 0.0, 'episode_reward_mean': np.float64(0.0), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 1000, 'policy_reward_min': {'default_policy': np.float64(0.0)}, 'policy_reward_max': {'default_policy': np.float64(0.0)}, 'policy_reward_mean': {'default_policy': np.float64(0.0)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.2563342626569026), 'mean_inference_ms': np.float64(1.9697609169366008), 'mean_action_processing_ms': np.float64(0.07722510593270979), 'mean_env_wait_ms': np.float64(0.12046290137579843), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.00732421875), 'StateBufferConnector_ms': np.float64(0.004405975341796875), 'ViewRequirementAgentConnector_ms': np.float64(0.06720304489135742)}, 'num_episodes': 10, 'episode_return_max': 0.0, 'episode_return_min': 0.0, 'episode_return_mean': np.float64(0.0), 'episodes_this_iter': 10}, 'num_agent_steps_sampled_this_iter': 1000, 'num_env_steps_sampled_this_iter': 1000, 'timesteps_this_iter': 1000, 'num_healthy_workers': 1, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0}, 'custom_metrics': {}, 'episode_media': {}, 'info': {'learner': {'default_policy': {'learner_stats': {'allreduce_latency': np.float64(0.0), 'grad_gnorm': np.float64(0.20293236732963593), 'cur_kl_coeff': np.float64(0.3), 'cur_lr': np.float64(0.0003), 'total_loss': np.float64(-0.013446552103625671), 'policy_loss': np.float64(-0.0054155447310017), 'vf_loss': np.float64(0.0007048519046586607), 'vf_explained_var': np.float64(-1.0), 'kl': np.float64(0.004940095240054953), 'entropy': np.float64(0.986546342603622), 'entropy_coeff': np.float64(0.01)}, 'model': {}, 'custom_metrics': {}, 'num_agent_steps_trained': np.float64(128.0), 'num_grad_updates_lifetime': np.float64(2945.5), 'diff_num_grad_updates_vs_sampler_policy': np.float64(154.5)}}, 'num_env_steps_sampled': 40000, 'num_env_steps_trained': 40000, 'num_agent_steps_sampled': 40000, 'num_agent_steps_trained': 40000, 'num_env_steps_sampled_for_evaluation_this_iter': 1000}, 'env_runners': {'episode_reward_max': 0.005816978988381209, 'episode_reward_min': -0.022700817197977695, 'episode_reward_mean': np.float64(-0.002893244923410918), 'episode_len_mean': np.float64(100.0), 'episode_media': {}, 'episodes_timesteps_total': 10000, 'policy_reward_min': {'default_policy': np.float64(-0.022700817197977695)}, 'policy_reward_max': {'default_policy': np.float64(0.005816978988381209)}, 'policy_reward_mean': {'default_policy': np.float64(-0.002893244923410918)}, 'custom_metrics': {}, 'hist_stats': {'episode_reward': [-0.001139724436989448, -0.0021627484058214432, 0.003986554964156678, -0.0034899928055567367, 0.005816978988381209, 0.0014700477908321727, -0.0010211663761216724, -0.013882771964191973, -0.002407527388216802, -0.0021084717933117017, -0.010113166572809662, -0.011808421939538224, -0.00933585707818611, 0.005469459171603622, -0.009171670937009093, 0.0017863290430878698, 0.00035217265626385705, -0.0010854449593693943, -0.00011778598727345962, -0.014767690337379657, -0.00235831504355335, 0.00011829303198250887, 0.0003014322504954984, -0.00048006091610986595, 0.0015997125931317049, 0.001578278194510294, -0.005467624537765704, -0.000930691036056806, -0.013659533821885634, -0.020052121952178523, 0.0002903284573682937, 0.0024304025407495544, -0.01280405232192328, -0.0007193378162399632, -0.0029818607956436823, -0.000511229795241399, -0.0018433330559831807, -0.00791847921527569, 0.005717334566185489, 0.0004188914410341374, -0.004087515200671944, 0.0007460806750764562, -0.0014861041798936454, -0.0010816488917675515, -0.0012011567107797667, -0.006869472497056892, -0.006584832700714376, -0.0024135298379889415, -0.004370188227677759, 0.0003185213129546123, -0.004602008271818095, 0.0038331546670813278, -0.0005265921771274058, -0.0005479701603566985, 0.002315564391588869, -0.011306938174855306, -0.011893284206734219, 0.00019058992391489914, -0.008820770227449365, -0.0027528277187319414, 0.0005218281638917893, 0.0014665834698050287, -0.002111203809965172, 0.0024002928387318306, -0.0016726618669982707, -0.0008898012123015955, -0.0022215450898659377, -0.005117079524632435, 0.0013187318412409051, -0.022700817197977695, -0.008885856170372106, -0.0016519282352903796, -0.0002899214602968027, -0.0009733207385233152, -0.0020310288815470773, -0.00020170156563757355, -0.0008929229186882045, 0.0021170191880359665, -0.0009719180142277149, -8.901413267187706e-05, -0.00019512688021090102, -0.0013451515515707675, -0.003673360763820823, 0.002289563209659066, 0.0034557776650281846, -0.0014316514062059013, 0.0006810471275574704, -0.010053547089292403, 0.0004125136818990925, -0.013957565063073845, -0.00154964098829323, -0.0012913488673214173, -0.009213229663003876, -0.0024973588302350403, -0.00991351696420288, -0.009011280330383706, 0.002912949982728631, -0.010666483492910656, -0.0003417301159248879, 0.0010847071286340503], 'episode_lengths': [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], 'policy_default_policy_reward': [-0.001139724436989448, -0.0021627484058214432, 0.003986554964156678, -0.0034899928055567367, 0.005816978988381209, 0.0014700477908321727, -0.0010211663761216724, -0.013882771964191973, -0.002407527388216802, -0.0021084717933117017, -0.010113166572809662, -0.011808421939538224, -0.00933585707818611, 0.005469459171603622, -0.009171670937009093, 0.0017863290430878698, 0.00035217265626385705, -0.0010854449593693943, -0.00011778598727345962, -0.014767690337379657, -0.00235831504355335, 0.00011829303198250887, 0.0003014322504954984, -0.00048006091610986595, 0.0015997125931317049, 0.001578278194510294, -0.005467624537765704, -0.000930691036056806, -0.013659533821885634, -0.020052121952178523, 0.0002903284573682937, 0.0024304025407495544, -0.01280405232192328, -0.0007193378162399632, -0.0029818607956436823, -0.000511229795241399, -0.0018433330559831807, -0.00791847921527569, 0.005717334566185489, 0.0004188914410341374, -0.004087515200671944, 0.0007460806750764562, -0.0014861041798936454, -0.0010816488917675515, -0.0012011567107797667, -0.006869472497056892, -0.006584832700714376, -0.0024135298379889415, -0.004370188227677759, 0.0003185213129546123, -0.004602008271818095, 0.0038331546670813278, -0.0005265921771274058, -0.0005479701603566985, 0.002315564391588869, -0.011306938174855306, -0.011893284206734219, 0.00019058992391489914, -0.008820770227449365, -0.0027528277187319414, 0.0005218281638917893, 0.0014665834698050287, -0.002111203809965172, 0.0024002928387318306, -0.0016726618669982707, -0.0008898012123015955, -0.0022215450898659377, -0.005117079524632435, 0.0013187318412409051, -0.022700817197977695, -0.008885856170372106, -0.0016519282352903796, -0.0002899214602968027, -0.0009733207385233152, -0.0020310288815470773, -0.00020170156563757355, -0.0008929229186882045, 0.0021170191880359665, -0.0009719180142277149, -8.901413267187706e-05, -0.00019512688021090102, -0.0013451515515707675, -0.003673360763820823, 0.002289563209659066, 0.0034557776650281846, -0.0014316514062059013, 0.0006810471275574704, -0.010053547089292403, 0.0004125136818990925, -0.013957565063073845, -0.00154964098829323, -0.0012913488673214173, -0.009213229663003876, -0.0024973588302350403, -0.00991351696420288, -0.009011280330383706, 0.002912949982728631, -0.010666483492910656, -0.0003417301159248879, 0.0010847071286340503]}, 'sampler_perf': {'mean_raw_obs_processing_ms': np.float64(0.24527072259159183), 'mean_inference_ms': np.float64(1.7967967404149363), 'mean_action_processing_ms': np.float64(0.0668114490877102), 'mean_env_wait_ms': np.float64(0.09970113153504069), 'mean_env_render_ms': np.float64(0.0)}, 'num_faulty_episodes': 0, 'connector_metrics': {'ObsPreprocessorConnector_ms': np.float64(0.002383708953857422), 'StateBufferConnector_ms': np.float64(0.0022521018981933594), 'ViewRequirementAgentConnector_ms': np.float64(0.06951642036437988)}, 'num_episodes': 40, 'episode_return_max': 0.005816978988381209, 'episode_return_min': -0.022700817197977695, 'episode_return_mean': np.float64(-0.002893244923410918), 'episodes_this_iter': 40}, 'num_healthy_workers': 2, 'actor_manager_num_outstanding_async_reqs': 0, 'num_remote_worker_restarts': 0, 'num_agent_steps_sampled': 40000, 'num_agent_steps_trained': 40000, 'num_env_steps_sampled': 40000, 'num_env_steps_trained': 40000, 'num_env_steps_sampled_this_iter': 4000, 'num_env_steps_trained_this_iter': 4000, 'num_env_steps_sampled_throughput_per_sec': 75.42751580762028, 'num_env_steps_trained_throughput_per_sec': 75.42751580762028, 'timesteps_total': 40000, 'num_env_steps_sampled_lifetime': 40000, 'num_agent_steps_sampled_lifetime': 40000, 'num_steps_trained_this_iter': 4000, 'agent_timesteps_total': 40000, 'timers': {'training_iteration_time_ms': 52701.114, 'restore_workers_time_ms': 0.026, 'training_step_time_ms': 52701.022, 'sample_time_ms': 4688.309, 'load_time_ms': 4.084, 'load_throughput': 979474.102, 'learn_time_ms': 47997.048, 'learn_throughput': 83.338, 'synch_weights_time_ms': 10.972, 'restore_eval_workers_time_ms': 0.024, 'evaluation_iteration_time_ms': 2459.373, 'evaluation_iteration_throughput': 406.608}, 'counters': {'num_env_steps_sampled': 40000, 'num_env_steps_trained': 40000, 'num_agent_steps_sampled': 40000, 'num_agent_steps_trained': 40000, 'num_env_steps_sampled_for_evaluation_this_iter': 1000}, 'done': False, 'training_iteration': 10, 'trial_id': 'default', 'date': '2025-08-05_16-15-02', 'timestamp': 1754381702, 'time_this_iter_s': 53.04372024536133, 'time_total_s': 527.1085984706879, 'pid': 3294923, 'hostname': '208083', 'node_ip': '************', 'config': {'exploration_config': {'type': 'StochasticSampling'}, 'extra_python_environs_for_driver': {}, 'extra_python_environs_for_worker': {}, 'placement_strategy': 'PACK', 'num_gpus': 0, '_fake_gpus': False, 'num_cpus_for_main_process': 1, 'eager_tracing': True, 'eager_max_retraces': 20, 'tf_session_args': {'intra_op_parallelism_threads': 2, 'inter_op_parallelism_threads': 2, 'gpu_options': {'allow_growth': True}, 'log_device_placement': False, 'device_count': {'CPU': 1}, 'allow_soft_placement': True}, 'local_tf_session_args': {'intra_op_parallelism_threads': 8, 'inter_op_parallelism_threads': 8}, 'torch_compile_learner': False, 'torch_compile_learner_what_to_compile': <TorchCompileWhatToCompile.FORWARD_TRAIN: 'forward_train'>, 'torch_compile_learner_dynamo_backend': 'inductor', 'torch_compile_learner_dynamo_mode': None, 'torch_compile_worker': False, 'torch_compile_worker_dynamo_backend': 'onnxrt', 'torch_compile_worker_dynamo_mode': None, 'torch_ddp_kwargs': {}, 'torch_skip_nan_gradients': False, 'env': 'CryptoTradingEnv', 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700]}, 'observation_space': None, 'action_space': None, 'clip_rewards': None, 'normalize_actions': True, 'clip_actions': False, '_is_atari': None, 'disable_env_checking': False, 'render_env': False, 'action_mask_key': 'action_mask', 'env_runner_cls': None, 'num_env_runners': 2, 'create_local_env_runner': True, 'num_envs_per_env_runner': 1, 'gym_env_vectorize_mode': 'SYNC', 'num_cpus_per_env_runner': 1, 'num_gpus_per_env_runner': 0, 'custom_resources_per_env_runner': {}, 'validate_env_runners_after_construction': True, 'episodes_to_numpy': True, 'max_requests_in_flight_per_env_runner': 1, 'sample_timeout_s': 60.0, '_env_to_module_connector': None, 'add_default_connectors_to_env_to_module_pipeline': True, '_module_to_env_connector': None, 'add_default_connectors_to_module_to_env_pipeline': True, 'merge_env_runner_states': 'training_only', 'broadcast_env_runner_states': True, 'episode_lookback_horizon': 1, 'rollout_fragment_length': 168, 'batch_mode': 'complete_episodes', 'compress_observations': False, 'remote_worker_envs': False, 'remote_env_batch_wait_ms': 0, 'enable_tf1_exec_eagerly': False, 'sample_collector': <class 'ray.rllib.evaluation.collectors.simple_list_collector.SimpleListCollector'>, 'preprocessor_pref': 'deepmind', 'observation_filter': 'NoFilter', 'update_worker_filter_stats': True, 'use_worker_filter_stats': True, 'sampler_perf_stats_ema_coef': None, '_is_online': True, 'num_learners': 0, 'num_gpus_per_learner': 0, 'num_cpus_per_learner': 'auto', 'num_aggregator_actors_per_learner': 0, 'max_requests_in_flight_per_aggregator_actor': 3, 'local_gpu_idx': 0, 'max_requests_in_flight_per_learner': 3, 'gamma': 0.99, 'lr': 0.0003, 'grad_clip': 0.5, 'grad_clip_by': 'global_norm', '_train_batch_size_per_learner': None, 'train_batch_size': 4000, 'num_epochs': 10, 'minibatch_size': 128, 'shuffle_batch_per_epoch': True, 'model': {'fcnet_hiddens': [256, 256], 'fcnet_activation': 'tanh', 'fcnet_weights_initializer': None, 'fcnet_weights_initializer_config': None, 'fcnet_bias_initializer': None, 'fcnet_bias_initializer_config': None, 'conv_filters': None, 'conv_activation': 'relu', 'conv_kernel_initializer': None, 'conv_kernel_initializer_config': None, 'conv_bias_initializer': None, 'conv_bias_initializer_config': None, 'conv_transpose_kernel_initializer': None, 'conv_transpose_kernel_initializer_config': None, 'conv_transpose_bias_initializer': None, 'conv_transpose_bias_initializer_config': None, 'post_fcnet_hiddens': [], 'post_fcnet_activation': 'relu', 'post_fcnet_weights_initializer': None, 'post_fcnet_weights_initializer_config': None, 'post_fcnet_bias_initializer': None, 'post_fcnet_bias_initializer_config': None, 'free_log_std': False, 'log_std_clip_param': 20.0, 'no_final_linear': False, 'vf_share_layers': True, 'use_lstm': False, 'max_seq_len': 20, 'lstm_cell_size': 256, 'lstm_use_prev_action': False, 'lstm_use_prev_reward': False, 'lstm_weights_initializer': None, 'lstm_weights_initializer_config': None, 'lstm_bias_initializer': None, 'lstm_bias_initializer_config': None, '_time_major': False, 'use_attention': False, 'attention_num_transformer_units': 1, 'attention_dim': 64, 'attention_num_heads': 1, 'attention_head_dim': 32, 'attention_memory_inference': 50, 'attention_memory_training': 50, 'attention_position_wise_mlp_dim': 32, 'attention_init_gru_gate_bias': 2.0, 'attention_use_n_prev_actions': 0, 'attention_use_n_prev_rewards': 0, 'framestack': True, 'dim': 84, 'grayscale': False, 'zero_mean': True, 'custom_model': 'transformer', 'custom_model_config': {'model_type': 'transformer', 'input_size': 100, 'hidden_size': 256, 'output_size': 3, 'num_layers': 2, 'num_heads': 4, 'dropout': 0.1, 'activation': 'gelu', 'learning_rate': 0.0003, 'weight_decay': 1e-05, 'gradient_clip': 1.0, 'd_model': 128, 'lookback_window': 24, 'feature_dim': None}, 'custom_action_dist': None, 'custom_preprocessor': None, 'encoder_latent_dim': None, 'always_check_shapes': False, 'lstm_use_prev_action_reward': -1, '_use_default_native_models': -1, '_disable_preprocessor_api': False, '_disable_action_flattening': False}, '_learner_connector': None, 'add_default_connectors_to_learner_pipeline': True, 'learner_config_dict': {}, 'optimizer': {}, '_learner_class': None, 'callbacks_on_algorithm_init': None, 'callbacks_on_env_runners_recreated': None, 'callbacks_on_offline_eval_runners_recreated': None, 'callbacks_on_checkpoint_loaded': None, 'callbacks_on_environment_created': None, 'callbacks_on_episode_created': None, 'callbacks_on_episode_start': None, 'callbacks_on_episode_step': None, 'callbacks_on_episode_end': None, 'callbacks_on_evaluate_start': None, 'callbacks_on_evaluate_end': None, 'callbacks_on_evaluate_offline_start': None, 'callbacks_on_evaluate_offline_end': None, 'callbacks_on_sample_end': None, 'callbacks_on_train_result': None, 'explore': True, 'enable_rl_module_and_learner': False, 'enable_env_runner_and_connector_v2': False, '_prior_exploration_config': None, 'count_steps_by': 'env_steps', 'policy_map_capacity': 100, 'policy_mapping_fn': <function AlgorithmConfig.DEFAULT_POLICY_MAPPING_FN at 0x7f08a92f4180>, 'policies_to_train': None, 'policy_states_are_swappable': False, 'observation_fn': None, 'offline_data_class': None, 'input_read_method': 'read_parquet', 'input_read_method_kwargs': {}, 'input_read_schema': {}, 'input_read_episodes': False, 'input_read_sample_batches': False, 'input_read_batch_size': None, 'input_filesystem': None, 'input_filesystem_kwargs': {}, 'input_compress_columns': ['obs', 'new_obs'], 'input_spaces_jsonable': True, 'materialize_data': False, 'materialize_mapped_data': True, 'map_batches_kwargs': {}, 'iter_batches_kwargs': {}, 'ignore_final_observation': False, 'prelearner_class': None, 'prelearner_buffer_class': None, 'prelearner_buffer_kwargs': {}, 'prelearner_module_synch_period': 10, 'dataset_num_iters_per_learner': None, 'input_config': {}, 'actions_in_input_normalized': False, 'postprocess_inputs': False, 'shuffle_buffer_size': 0, 'output': None, 'output_config': {}, 'output_compress_columns': ['obs', 'new_obs'], 'output_max_file_size': 67108864, 'output_max_rows_per_file': None, 'output_write_remaining_data': False, 'output_write_method': 'write_parquet', 'output_write_method_kwargs': {}, 'output_filesystem': None, 'output_filesystem_kwargs': {}, 'output_write_episodes': True, 'offline_sampling': False, 'evaluation_interval': 10, 'evaluation_duration': 10, 'evaluation_duration_unit': 'episodes', 'evaluation_sample_timeout_s': 120.0, 'evaluation_auto_duration_min_env_steps_per_sample': 100, 'evaluation_auto_duration_max_env_steps_per_sample': 2000, 'evaluation_parallel_to_training': True, 'evaluation_force_reset_envs_before_iteration': True, 'evaluation_config': {'explore': False, 'env_config': {'initial_balance': 10000.0, 'leverage': 1.0, 'transaction_cost': 0.001, 'slippage': 0.0001, 'symbols': ['BTC/USDT'], 'timeframe': '1h', 'features': ['close', 'volume', 'rsi', 'macd'], 'max_position_size': 1.0, 'stop_loss': None, 'take_profit': None, 'reward_type': 'pnl', 'reward_scaling': 1.0, 'dataset_name': 'sample_dataset', 'episode_length': 100, 'lookback_window': 24, 'train_start': [50], 'train_end': [500], 'test_start': [500], 'test_end': [700], 'regime': 'evaluation'}}, 'off_policy_estimation_methods': {}, 'ope_split_batch_by_episode': True, 'evaluation_num_env_runners': 1, 'in_evaluation': False, 'sync_filters_on_rollout_workers_timeout_s': 10.0, 'offline_evaluation_interval': None, 'num_offline_eval_runners': 0, 'offline_evaluation_type': None, 'offline_eval_runner_class': None, 'offline_loss_for_module_fn': None, 'offline_evaluation_duration': 1, 'offline_evaluation_parallel_to_training': False, 'offline_evaluation_timeout_s': 120.0, 'num_cpus_per_offline_eval_runner': 1, 'num_gpus_per_offline_eval_runner': 0, 'custom_resources_per_offline_eval_runner': {}, 'restart_failed_offline_eval_runners': True, 'ignore_offline_eval_runner_failures': False, 'max_num_offline_eval_runner_restarts': 1000, 'offline_eval_runner_restore_timeout_s': 1800.0, 'max_requests_in_flight_per_offline_eval_runner': 1, 'validate_offline_eval_runners_after_construction': True, 'offline_eval_runner_health_probe_timeout_s': 30.0, 'offline_eval_rl_module_inference_only': False, 'broadcast_offline_eval_runner_states': False, 'offline_eval_batch_size_per_runner': 256, 'dataset_num_iters_per_eval_runner': 1, 'keep_per_episode_custom_metrics': False, 'metrics_episode_collection_timeout_s': 60.0, 'metrics_num_episodes_for_smoothing': 100, 'min_time_s_per_iteration': None, 'min_train_timesteps_per_iteration': 0, 'min_sample_timesteps_per_iteration': 0, 'log_gradients': True, 'export_native_model_files': False, 'checkpoint_trainable_policies_only': False, 'logger_creator': None, 'logger_config': None, 'log_level': 'WARN', 'log_sys_usage': True, 'fake_sampler': False, 'seed': 42, 'restart_failed_env_runners': True, 'ignore_env_runner_failures': False, 'max_num_env_runner_restarts': 1000, 'delay_between_env_runner_restarts_s': 60.0, 'restart_failed_sub_environments': False, 'num_consecutive_env_runner_failures_tolerance': 100, 'env_runner_health_probe_timeout_s': 30.0, 'env_runner_restore_timeout_s': 1800.0, '_model_config': {}, '_rl_module_spec': None, 'algorithm_config_overrides_per_module': {}, '_per_module_overrides': {}, '_validate_config': True, '_use_msgpack_checkpoints': False, '_torch_grad_scaler_class': None, '_torch_lr_scheduler_classes': None, '_tf_policy_handles_more_than_one_loss': False, '_disable_preprocessor_api': False, '_disable_action_flattening': False, '_disable_initialize_loss_from_dummy_batch': False, '_dont_auto_sync_env_runner_states': False, 'env_task_fn': -1, 'enable_connectors': -1, 'simple_optimizer': False, 'policy_map_cache': -1, 'worker_cls': -1, 'synchronize_filters': -1, 'enable_async_evaluation': -1, 'custom_async_evaluation_function': -1, '_enable_rl_module_api': -1, 'auto_wrap_old_gym_envs': -1, 'always_attach_evaluation_results': -1, 'replay_sequence_length': None, '_disable_execution_plan_api': -1, 'use_critic': True, 'use_gae': True, 'use_kl_loss': True, 'kl_coeff': 0.2, 'kl_target': 0.01, 'vf_loss_coeff': 0.5, 'entropy_coeff': 0.01, 'clip_param': 0.2, 'vf_clip_param': 10.0, 'entropy_coeff_schedule': None, 'lr_schedule': None, 'sgd_minibatch_size': -1, 'vf_share_layers': -1, 'class': <class 'ray.rllib.algorithms.ppo.ppo.PPOConfig'>, 'lambda': 0.95, 'input': 'sampler', 'policies': {'default_policy': (None, None, None, None)}, 'callbacks': <class 'ray.rllib.callbacks.callbacks.RLlibCallback'>, 'create_env_on_driver': False, 'custom_eval_function': None, 'framework': 'torch'}, 'time_since_restore': 527.1085984706879, 'iterations_since_restore': 10, 'perf': {'cpu_util_percent': np.float64(45.93970588235294), 'ram_util_percent': np.float64(53.8220588235294), 'gpu_util_percent0': np.float64(0.0), 'vram_util_percent0': np.float64(0.048665364583333336), 'gpu_util_percent1': np.float64(0.3394117647058823), 'vram_util_percent1': np.float64(0.4925130208333335), 'gpu_util_percent2': np.float64(0.2291176470588235), 'vram_util_percent2': np.float64(0.4935709635416665), 'gpu_util_percent3': np.float64(0.0), 'vram_util_percent3': np.float64(8.138020833333332e-05), 'gpu_util_percent4': np.float64(0.0), 'vram_util_percent4': np.float64(8.138020833333332e-05), 'gpu_util_percent5': np.float64(0.0), 'vram_util_percent5': np.float64(8.138020833333332e-05), 'gpu_util_percent6': np.float64(0.0), 'vram_util_percent6': np.float64(8.138020833333332e-05)}})
2025-08-05 16:15:06,109 - rl_ct.scripts.train - INFO - train.py:137 - Training completed successfully
2025-08-05 16:15:08,609 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:342 - PPOAgent closed
2025-08-05 16:15:08,613 - __main__ - INFO - quick_start.py:191 - Training completed. Best model: checkpoints/quick_start_demo/best_model_step_10000.zip
2025-08-05 16:15:08,616 - __main__ - INFO - quick_start.py:206 - Running model evaluation...
2025-08-05 16:15:08,619 - __main__ - ERROR - quick_start.py:232 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 16:15:08,622 - __main__ - INFO - quick_start.py:233 - Skipping evaluation for demo
2025-08-05 16:15:08,625 - __main__ - INFO - quick_start.py:238 - Running backtesting...
2025-08-05 16:15:08,627 - __main__ - ERROR - quick_start.py:262 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 16:15:08,630 - __main__ - INFO - quick_start.py:263 - Skipping backtesting for demo
2025-08-05 16:15:08,633 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 16:15:08,635 - __main__ - INFO - quick_start.py:295 - Quick start demo completed successfully!
2025-08-05 16:15:08,638 - __main__ - INFO - quick_start.py:296 - ============================================================
2025-08-05 16:15:08,640 - __main__ - INFO - quick_start.py:297 - Next steps:
2025-08-05 16:15:08,643 - __main__ - INFO - quick_start.py:298 - 1. Check results in 'results/' directory
2025-08-05 16:15:08,645 - __main__ - INFO - quick_start.py:299 - 2. Modify configs for your specific use case
2025-08-05 16:15:08,648 - __main__ - INFO - quick_start.py:300 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 16:15:08,651 - __main__ - INFO - quick_start.py:301 - 4. Train on larger datasets with 'rl-ct train'
