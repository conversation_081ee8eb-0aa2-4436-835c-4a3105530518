2025-08-05 10:17:15,534 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:17:15,541 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:17:15,542 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:17:15,543 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:17:15,545 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:17:15,569 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:17:15,571 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:17:15,573 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:17:15,574 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:17:15,582 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:17:15,583 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:17:15,657 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:17:15,660 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:17:15,669 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:17:15,672 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:17:15,676 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:17:15,678 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:17:15,679 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:17:15,687 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:17:15,690 - __main__ - ERROR - quick_start.py:306 - Demo failed: No processed data to save
2025-08-05 10:17:15,691 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:17:40,817 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:17:40,821 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:17:40,822 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:17:40,823 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:17:40,825 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:17:40,841 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:17:40,843 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:17:40,844 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:17:40,845 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:17:40,851 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:17:40,852 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:17:40,914 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:17:40,916 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:17:40,922 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:17:40,923 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:17:40,926 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:17:40,927 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:17:40,929 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:17:40,933 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:17:40,934 - __main__ - ERROR - quick_start.py:306 - Demo failed: No processed data to save
2025-08-05 10:17:40,936 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:18:35,455 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:18:35,459 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:18:35,460 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:18:35,462 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:18:35,463 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:18:35,478 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:18:35,480 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:18:35,481 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:18:35,483 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:18:35,488 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:18:35,489 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:18:35,548 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:18:35,549 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:18:35,555 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:18:35,556 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:18:35,559 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:18:35,561 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:18:35,562 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:18:35,566 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:18:35,568 - __main__ - ERROR - quick_start.py:306 - Demo failed: No processed data to save
2025-08-05 10:18:35,569 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:19:36,464 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:19:36,468 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:19:36,469 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:19:36,470 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:19:36,471 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:19:36,490 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:19:36,492 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:19:36,494 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:19:36,496 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:19:36,504 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:19:36,505 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:19:36,565 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:19:36,567 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:19:36,573 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:19:36,574 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:19:36,577 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:19:36,578 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:19:36,579 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:19:36,584 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:19:36,585 - __main__ - ERROR - quick_start.py:306 - Demo failed: No processed data to save
2025-08-05 10:19:36,587 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:26:14,602 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:26:14,606 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:26:14,607 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:26:14,609 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:26:14,610 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:26:14,626 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:26:14,628 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:26:14,629 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:26:14,631 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:26:14,636 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:26:14,637 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:26:14,697 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:26:14,698 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:26:14,704 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:26:14,705 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:26:14,708 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:26:14,710 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:26:14,711 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:26:14,715 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:26:14,717 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:26:14,720 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:26:14,721 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:26:14,723 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:26:14,724 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:26:14,725 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:26:14,730 - __main__ - ERROR - quick_start.py:306 - Demo failed: Missing key ray
    full_key: ray
    object_type=dict
2025-08-05 10:26:14,732 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:28:55,545 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:28:55,548 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:28:55,550 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:28:55,551 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:28:55,552 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:28:55,569 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:28:55,570 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:28:55,571 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:28:55,573 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:28:55,578 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:28:55,580 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:28:55,640 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:28:55,641 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:28:55,647 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:28:55,648 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:28:55,651 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:28:55,652 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:28:55,654 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:28:55,657 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:28:55,659 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:28:55,663 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:28:55,664 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:28:55,665 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:28:55,666 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:28:55,668 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:28:55,673 - __main__ - ERROR - quick_start.py:306 - Demo failed: Missing key ray
    full_key: training.ray
    object_type=dict
2025-08-05 10:28:55,674 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:31:10,225 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:31:10,228 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 10:31:10,230 - __main__ - INFO - quick_start.py:274 - RL-CT Framework Quick Start Demo
2025-08-05 10:31:10,231 - __main__ - INFO - quick_start.py:275 - ============================================================
2025-08-05 10:31:10,232 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:31:10,248 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:31:10,250 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:31:10,251 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:31:10,252 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:31:10,258 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:31:10,259 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:31:10,319 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:31:10,321 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:31:10,326 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:31:10,328 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:31:10,331 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:31:10,332 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:31:10,333 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:31:10,337 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:31:10,339 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:31:10,342 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:31:10,344 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:31:10,345 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:31:10,346 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:31:10,347 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:31:10,352 - __main__ - ERROR - quick_start.py:306 - Demo failed: Missing key ray
    full_key: training.ray
    object_type=dict
2025-08-05 10:31:10,354 - __main__ - INFO - quick_start.py:307 - Please check the logs for more details
2025-08-05 10:31:21,202 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:31:21,207 - __main__ - INFO - quick_start.py:269 - ============================================================
2025-08-05 10:31:21,208 - __main__ - INFO - quick_start.py:270 - RL-CT Framework Quick Start Demo
2025-08-05 10:31:21,209 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 10:31:21,210 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:31:21,230 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:31:21,231 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:31:21,233 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:31:21,234 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:31:21,240 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:31:21,242 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:31:21,301 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:31:21,302 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:31:21,308 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:31:21,310 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:31:21,313 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:31:21,314 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:31:21,315 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:31:21,319 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:31:21,321 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:31:21,325 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:31:21,326 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:31:21,328 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:31:21,329 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:31:21,330 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:31:21,338 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 10:31:21,340 - __main__ - INFO - quick_start.py:174 - Training config saved: configs/training/quick_start.yaml
2025-08-05 10:31:21,341 - __main__ - INFO - quick_start.py:180 - Starting model training...
2025-08-05 10:31:21,348 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 10:34:03,296 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:34:03,300 - __main__ - INFO - quick_start.py:269 - ============================================================
2025-08-05 10:34:03,301 - __main__ - INFO - quick_start.py:270 - RL-CT Framework Quick Start Demo
2025-08-05 10:34:03,302 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 10:34:03,304 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:34:03,320 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:34:03,321 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:34:03,323 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:34:03,324 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:34:03,330 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:34:03,331 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:34:03,390 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:34:03,391 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:34:03,397 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:34:03,398 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:34:03,401 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:34:03,403 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:34:03,404 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:34:03,408 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:34:03,410 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:34:03,413 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:34:03,415 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:34:03,416 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:34:03,417 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:34:03,418 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:34:03,426 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 10:34:03,427 - __main__ - INFO - quick_start.py:174 - Training config saved: configs/training/quick_start.yaml
2025-08-05 10:34:03,428 - __main__ - INFO - quick_start.py:180 - Starting model training...
2025-08-05 10:34:03,435 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 10:42:30,114 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:42:30,118 - __main__ - INFO - quick_start.py:269 - ============================================================
2025-08-05 10:42:30,120 - __main__ - INFO - quick_start.py:270 - RL-CT Framework Quick Start Demo
2025-08-05 10:42:30,121 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 10:42:30,122 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:42:30,138 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:42:30,140 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:42:30,141 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:42:30,143 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:42:30,148 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:42:30,150 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:42:30,212 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:42:30,215 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:42:30,221 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:42:30,223 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:42:30,226 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:42:30,227 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:42:30,228 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:42:30,233 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:42:30,235 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:42:30,238 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:42:30,240 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:42:30,241 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:42:30,242 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:42:30,244 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:42:30,251 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 10:42:30,253 - __main__ - INFO - quick_start.py:174 - Training config saved: configs/training/quick_start.yaml
2025-08-05 10:42:30,254 - __main__ - INFO - quick_start.py:180 - Starting model training...
2025-08-05 10:42:30,261 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 10:48:09,590 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:48:09,595 - __main__ - INFO - quick_start.py:269 - ============================================================
2025-08-05 10:48:09,596 - __main__ - INFO - quick_start.py:270 - RL-CT Framework Quick Start Demo
2025-08-05 10:48:09,597 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 10:48:09,598 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:48:09,614 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:48:09,616 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:48:09,617 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:48:09,619 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:48:09,624 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:48:09,625 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:48:09,692 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:48:09,694 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:48:09,701 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:48:09,702 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:48:09,705 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:48:09,707 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:48:09,708 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:48:09,712 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:48:09,714 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:48:09,718 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:48:09,720 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:48:09,721 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:48:09,722 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:48:09,724 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:48:09,732 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 10:48:09,733 - __main__ - INFO - quick_start.py:174 - Training config saved: configs/training/quick_start.yaml
2025-08-05 10:48:09,735 - __main__ - INFO - quick_start.py:180 - Starting model training...
2025-08-05 10:48:09,742 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 10:57:18,677 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 10:57:18,681 - __main__ - INFO - quick_start.py:269 - ============================================================
2025-08-05 10:57:18,682 - __main__ - INFO - quick_start.py:270 - RL-CT Framework Quick Start Demo
2025-08-05 10:57:18,684 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 10:57:18,685 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 10:57:18,701 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 10:57:18,702 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 10:57:18,704 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 10:57:18,705 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 10:57:18,710 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 10:57:18,712 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 10:57:18,770 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 10:57:18,771 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 10:57:18,777 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 10:57:18,778 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 10:57:18,781 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 10:57:18,783 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 10:57:18,784 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 10:57:18,788 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 10:57:18,790 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 10:57:18,793 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 10:57:18,795 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 10:57:18,796 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 10:57:18,797 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 10:57:18,798 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 10:57:18,805 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 10:57:18,807 - __main__ - INFO - quick_start.py:174 - Training config saved: configs/training/quick_start.yaml
2025-08-05 10:57:18,808 - __main__ - INFO - quick_start.py:180 - Starting model training...
2025-08-05 10:57:18,815 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:03:30,828 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:03:30,833 - __main__ - INFO - quick_start.py:269 - ============================================================
2025-08-05 11:03:30,834 - __main__ - INFO - quick_start.py:270 - RL-CT Framework Quick Start Demo
2025-08-05 11:03:30,835 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 11:03:30,836 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:03:30,856 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:03:30,857 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:03:30,859 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:03:30,860 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:03:30,867 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:03:30,868 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:03:30,933 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:03:30,935 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:03:30,943 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:03:30,945 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:03:30,949 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:03:30,951 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:03:30,952 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:03:30,958 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:03:30,961 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:03:30,964 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:03:30,966 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:03:30,967 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:03:30,968 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:03:30,969 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:03:30,978 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:03:30,980 - __main__ - INFO - quick_start.py:174 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:03:30,981 - __main__ - INFO - quick_start.py:180 - Starting model training...
2025-08-05 11:03:30,989 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:06:49,562 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:06:49,566 - __main__ - INFO - quick_start.py:270 - ============================================================
2025-08-05 11:06:49,567 - __main__ - INFO - quick_start.py:271 - RL-CT Framework Quick Start Demo
2025-08-05 11:06:49,569 - __main__ - INFO - quick_start.py:272 - ============================================================
2025-08-05 11:06:49,570 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:06:49,586 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:06:49,587 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:06:49,589 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:06:49,590 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:06:49,595 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:06:49,597 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:06:49,656 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:06:49,657 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:06:49,663 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:06:49,664 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:06:49,667 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:06:49,669 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:06:49,670 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:06:49,674 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:06:49,675 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:06:49,678 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:06:49,679 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:06:49,680 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:06:49,681 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:06:49,683 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:06:49,690 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:06:49,691 - __main__ - INFO - quick_start.py:175 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:06:49,693 - __main__ - INFO - quick_start.py:181 - Starting model training...
2025-08-05 11:06:49,700 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:16:34,445 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:16:34,449 - __main__ - INFO - quick_start.py:270 - ============================================================
2025-08-05 11:16:34,451 - __main__ - INFO - quick_start.py:271 - RL-CT Framework Quick Start Demo
2025-08-05 11:16:34,452 - __main__ - INFO - quick_start.py:272 - ============================================================
2025-08-05 11:16:34,453 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:16:34,469 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:16:34,470 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:16:34,472 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:16:34,473 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:16:34,478 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:16:34,480 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:16:34,538 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:16:34,539 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:16:34,545 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:16:34,546 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:16:34,549 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:16:34,551 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:16:34,552 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:16:34,556 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:16:34,557 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:16:34,560 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:16:34,561 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:16:34,562 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:16:34,564 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:16:34,565 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:16:34,572 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:16:34,573 - __main__ - INFO - quick_start.py:175 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:16:34,574 - __main__ - INFO - quick_start.py:181 - Starting model training...
2025-08-05 11:16:34,581 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:29:52,786 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:29:52,790 - __main__ - INFO - quick_start.py:270 - ============================================================
2025-08-05 11:29:52,791 - __main__ - INFO - quick_start.py:271 - RL-CT Framework Quick Start Demo
2025-08-05 11:29:52,792 - __main__ - INFO - quick_start.py:272 - ============================================================
2025-08-05 11:29:52,794 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:29:52,809 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:29:52,811 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:29:52,812 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:29:52,814 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:29:52,819 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:29:52,820 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:29:52,879 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:29:52,880 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:29:52,886 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:29:52,887 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:29:52,891 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:29:52,893 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:29:52,894 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:29:52,898 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:29:52,900 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:29:52,902 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:29:52,903 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:29:52,904 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:29:52,906 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:29:52,907 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:29:52,914 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:29:52,915 - __main__ - INFO - quick_start.py:175 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:29:52,917 - __main__ - INFO - quick_start.py:181 - Starting model training...
2025-08-05 11:29:52,923 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:35:41,079 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:35:41,083 - __main__ - INFO - quick_start.py:270 - ============================================================
2025-08-05 11:35:41,084 - __main__ - INFO - quick_start.py:271 - RL-CT Framework Quick Start Demo
2025-08-05 11:35:41,085 - __main__ - INFO - quick_start.py:272 - ============================================================
2025-08-05 11:35:41,087 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:35:41,102 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:35:41,104 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:35:41,105 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:35:41,107 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:35:41,112 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:35:41,113 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:35:41,174 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:35:41,176 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:35:41,182 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:35:41,184 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:35:41,187 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:35:41,188 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:35:41,190 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:35:41,194 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:35:41,196 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:35:41,201 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:35:41,203 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:35:41,204 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:35:41,205 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:35:41,206 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:35:41,214 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:35:41,216 - __main__ - INFO - quick_start.py:175 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:35:41,217 - __main__ - INFO - quick_start.py:181 - Starting model training...
2025-08-05 11:35:41,224 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:37:57,545 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:37:57,549 - __main__ - INFO - quick_start.py:270 - ============================================================
2025-08-05 11:37:57,550 - __main__ - INFO - quick_start.py:271 - RL-CT Framework Quick Start Demo
2025-08-05 11:37:57,552 - __main__ - INFO - quick_start.py:272 - ============================================================
2025-08-05 11:37:57,553 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:37:57,568 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:37:57,570 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:37:57,571 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:37:57,573 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:37:57,578 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:37:57,579 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:37:57,638 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:37:57,639 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:37:57,645 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:37:57,646 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:37:57,649 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:37:57,651 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:37:57,652 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:37:57,656 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:37:57,657 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:37:57,661 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:37:57,662 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:37:57,663 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:37:57,664 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:37:57,666 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:37:57,673 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:37:57,674 - __main__ - INFO - quick_start.py:175 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:37:57,676 - __main__ - INFO - quick_start.py:181 - Starting model training...
2025-08-05 11:37:57,682 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:38:35,145 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:38:35,149 - __main__ - INFO - quick_start.py:270 - ============================================================
2025-08-05 11:38:35,150 - __main__ - INFO - quick_start.py:271 - RL-CT Framework Quick Start Demo
2025-08-05 11:38:35,151 - __main__ - INFO - quick_start.py:272 - ============================================================
2025-08-05 11:38:35,152 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:38:35,168 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:38:35,169 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:38:35,171 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:38:35,172 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:38:35,177 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:38:35,179 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:38:35,238 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:38:35,239 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:38:35,245 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:38:35,246 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:38:35,249 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:38:35,250 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:38:35,252 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:38:35,256 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:38:35,257 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:38:35,260 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:38:35,262 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:38:35,263 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:38:35,264 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:38:35,265 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:38:35,273 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:38:35,274 - __main__ - INFO - quick_start.py:175 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:38:35,275 - __main__ - INFO - quick_start.py:181 - Starting model training...
2025-08-05 11:38:35,282 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:40:41,948 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:40:41,952 - __main__ - INFO - quick_start.py:270 - ============================================================
2025-08-05 11:40:41,953 - __main__ - INFO - quick_start.py:271 - RL-CT Framework Quick Start Demo
2025-08-05 11:40:41,955 - __main__ - INFO - quick_start.py:272 - ============================================================
2025-08-05 11:40:41,956 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:40:41,972 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:40:41,975 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:40:41,977 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:40:41,978 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:40:41,984 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:40:41,985 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:40:42,044 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:40:42,046 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:40:42,051 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:40:42,053 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:40:42,056 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:40:42,057 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:40:42,058 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:40:42,062 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:40:42,064 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:40:42,066 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:40:42,067 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:40:42,069 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:40:42,070 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:40:42,071 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:40:42,078 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:40:42,079 - __main__ - INFO - quick_start.py:175 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:40:42,081 - __main__ - INFO - quick_start.py:181 - Starting model training...
2025-08-05 11:40:42,088 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:43:20,907 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:43:20,911 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 11:43:20,912 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 11:43:20,914 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 11:43:20,915 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:43:20,932 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:43:20,933 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:43:20,935 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:43:20,936 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:43:20,941 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:43:20,943 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:43:21,002 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:43:21,003 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:43:21,009 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:43:21,010 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:43:21,013 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:43:21,015 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:43:21,016 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:43:21,020 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:43:21,022 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:43:21,025 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:43:21,026 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:43:21,027 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:43:21,028 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:43:21,030 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:43:21,037 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:43:21,038 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:43:21,039 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 11:43:21,046 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 11:55:34,320 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 11:55:34,324 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 11:55:34,325 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 11:55:34,326 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 11:55:34,327 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 11:55:34,344 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 11:55:34,345 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 11:55:34,347 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 11:55:34,348 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 11:55:34,353 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 11:55:34,355 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 11:55:34,425 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 11:55:34,427 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 11:55:34,433 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 11:55:34,434 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 11:55:34,437 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 11:55:34,439 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 11:55:34,440 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 11:55:34,444 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 11:55:34,446 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 11:55:34,449 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 11:55:34,450 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 11:55:34,451 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 11:55:34,453 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 11:55:34,454 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 11:55:34,462 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 11:55:34,463 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 11:55:34,464 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 11:55:34,471 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 15:27:37,360 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 15:27:37,364 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 15:27:37,365 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 15:27:37,367 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 15:27:37,368 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 15:27:37,384 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 15:27:37,386 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 15:27:37,387 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 15:27:37,389 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 15:27:37,394 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 15:27:37,395 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 15:27:37,455 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 15:27:37,456 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 15:27:37,462 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 15:27:37,463 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 15:27:37,466 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 15:27:37,468 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 15:27:37,469 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 15:27:37,473 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 15:27:37,475 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 15:27:37,479 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 15:27:37,480 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 15:27:37,481 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 15:27:37,482 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 15:27:37,484 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 15:27:37,491 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 15:27:37,492 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 15:27:37,494 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 15:27:37,501 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 15:27:53,382 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 15:27:53,386 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 15:27:53,387 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 15:27:53,388 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 15:27:53,390 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 15:27:53,406 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 15:27:53,408 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 15:27:53,409 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 15:27:53,411 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 15:27:53,416 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 15:27:53,418 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 15:27:53,478 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 15:27:53,480 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 15:27:53,486 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 15:27:53,487 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 15:27:53,490 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 15:27:53,491 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 15:27:53,493 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 15:27:53,497 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 15:27:53,498 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 15:27:53,502 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 15:27:53,503 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 15:27:53,504 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 15:27:53,505 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 15:27:53,507 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 15:27:53,514 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 15:27:53,515 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 15:27:53,517 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 15:27:53,523 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 15:32:19,306 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 15:32:19,311 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 15:32:19,312 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 15:32:19,313 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 15:32:19,315 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 15:32:19,334 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 15:32:19,335 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 15:32:19,337 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 15:32:19,338 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 15:32:19,344 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 15:32:19,346 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 15:32:19,406 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 15:32:19,407 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 15:32:19,414 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 15:32:19,419 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 15:32:19,422 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 15:32:19,423 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 15:32:19,424 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 15:32:19,429 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 15:32:19,431 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 15:32:19,433 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 15:32:19,435 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 15:32:19,436 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 15:32:19,437 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 15:32:19,438 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 15:32:19,446 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 15:32:19,447 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 15:32:19,448 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 15:32:19,455 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 15:55:40,220 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 15:55:40,224 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 15:55:40,225 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 15:55:40,226 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 15:55:40,227 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 15:55:40,243 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 15:55:40,244 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 15:55:40,246 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 15:55:40,247 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 15:55:40,252 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 15:55:40,254 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 15:55:40,313 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 15:55:40,315 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 15:55:40,320 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 15:55:40,322 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 15:55:40,325 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 15:55:40,327 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 15:55:40,328 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 15:55:40,332 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 15:55:40,335 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 15:55:40,338 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 15:55:40,339 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 15:55:40,340 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 15:55:40,342 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 15:55:40,343 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 15:55:40,350 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 15:55:40,352 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 15:55:40,353 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 15:55:40,360 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 15:59:20,376 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 15:59:20,382 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 15:59:20,384 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 15:59:20,385 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 15:59:20,386 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 15:59:20,404 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 15:59:20,405 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 15:59:20,407 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 15:59:20,408 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 15:59:20,414 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 15:59:20,415 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 15:59:20,475 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 15:59:20,477 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 15:59:20,483 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 15:59:20,484 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 15:59:20,487 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 15:59:20,488 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 15:59:20,490 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 15:59:20,494 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 15:59:20,495 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 15:59:20,499 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 15:59:20,500 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 15:59:20,501 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 15:59:20,503 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 15:59:20,504 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 15:59:20,511 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 15:59:20,513 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 15:59:20,514 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 15:59:20,521 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 16:01:19,855 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 16:01:19,859 - __main__ - INFO - quick_start.py:272 - ============================================================
2025-08-05 16:01:19,861 - __main__ - INFO - quick_start.py:273 - RL-CT Framework Quick Start Demo
2025-08-05 16:01:19,862 - __main__ - INFO - quick_start.py:274 - ============================================================
2025-08-05 16:01:19,863 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 16:01:19,882 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 16:01:19,883 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 16:01:19,885 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 16:01:19,886 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 16:01:19,893 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 16:01:19,894 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 16:01:19,954 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 16:01:19,956 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 16:01:19,962 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 16:01:19,963 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 16:01:19,966 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 16:01:19,967 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 16:01:19,969 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 16:01:19,973 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 16:01:19,974 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 16:01:19,977 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 16:01:19,978 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 16:01:19,979 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 16:01:19,981 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 16:01:19,982 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 16:01:19,990 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 16:01:19,991 - __main__ - INFO - quick_start.py:177 - Training config saved: configs/training/quick_start.yaml
2025-08-05 16:01:19,993 - __main__ - INFO - quick_start.py:183 - Starting model training...
2025-08-05 16:01:20,000 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 16:03:36,450 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 16:03:36,454 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 16:03:36,455 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 16:03:36,457 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 16:03:36,458 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 16:03:36,476 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 16:03:36,478 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 16:03:36,479 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 16:03:36,480 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 16:03:36,487 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 16:03:36,488 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 16:03:36,548 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 16:03:36,550 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 16:03:36,555 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 16:03:36,557 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 16:03:36,560 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 16:03:36,561 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 16:03:36,562 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 16:03:36,566 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 16:03:36,568 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 16:03:36,571 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 16:03:36,573 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 16:03:36,574 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 16:03:36,575 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 16:03:36,576 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 16:03:36,584 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 16:03:36,586 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 16:03:36,587 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 16:03:36,595 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
2025-08-05 16:05:42,971 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start.log
2025-08-05 16:05:42,975 - __main__ - INFO - quick_start.py:271 - ============================================================
2025-08-05 16:05:42,976 - __main__ - INFO - quick_start.py:272 - RL-CT Framework Quick Start Demo
2025-08-05 16:05:42,978 - __main__ - INFO - quick_start.py:273 - ============================================================
2025-08-05 16:05:42,979 - __main__ - INFO - quick_start.py:39 - Creating sample cryptocurrency data...
2025-08-05 16:05:42,995 - __main__ - INFO - quick_start.py:89 - Sample data created: data/raw/BTC_USDT_sample_data.csv
2025-08-05 16:05:42,996 - __main__ - INFO - quick_start.py:90 - Data shape: (720, 5)
2025-08-05 16:05:42,998 - __main__ - INFO - quick_start.py:91 - Price range: $22509.66 - $39162.82
2025-08-05 16:05:42,999 - __main__ - INFO - quick_start.py:96 - Processing sample data...
2025-08-05 16:05:43,004 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:153 - Creating features
2025-08-05 16:05:43,006 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:38 - Creating technical indicators
2025-08-05 16:05:43,064 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:61 - Created 54 technical indicators
2025-08-05 16:05:43,066 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:206 - Creating time features
2025-08-05 16:05:43,071 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:233 - Created 15 time features
2025-08-05 16:05:43,073 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:249 - Creating lag features for 2 columns with 3 lags
2025-08-05 16:05:43,076 - rl_ct.utils.preprocessing.feature_engineer - INFO - feature_engineer.py:258 - Created 6 lag features
2025-08-05 16:05:43,077 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:177 - Created 80 features
2025-08-05 16:05:43,078 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:197 - Scaling data
2025-08-05 16:05:43,082 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:212 - Scaled data shape: (720, 80)
2025-08-05 16:05:43,084 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:282 - Saving processed data to data/processed/sample_dataset
2025-08-05 16:05:43,087 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:169 - Saved dataset 'sample_dataset' to data/processed/sample_dataset
2025-08-05 16:05:43,089 - rl_ct.utils.preprocessing.data_processor - INFO - data_processor.py:301 - Processed data saved successfully
2025-08-05 16:05:43,090 - __main__ - INFO - quick_start.py:136 - Data processing completed
2025-08-05 16:05:43,091 - __main__ - INFO - quick_start.py:137 - Processed data shape: (720, 80)
2025-08-05 16:05:43,092 - __main__ - INFO - quick_start.py:142 - Creating training configuration...
2025-08-05 16:05:43,100 - rl_ct.utils.config - INFO - config.py:65 - Saved configuration to configs/training/quick_start.yaml
2025-08-05 16:05:43,101 - __main__ - INFO - quick_start.py:176 - Training config saved: configs/training/quick_start.yaml
2025-08-05 16:05:43,102 - __main__ - INFO - quick_start.py:182 - Starting model training...
2025-08-05 16:05:43,109 - rl_ct.utils.config - INFO - config.py:39 - Loaded configuration from configs/training/quick_start.yaml
