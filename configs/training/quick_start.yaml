training:
  env_name: CryptoTradingEnv
  dataset_name: default
  episode_length: 168
  lookback_window: 24
  algorithm: PPO
  total_timesteps: 50000
  learning_rate: 0.0003
  batch_size: 64
  n_epochs: 10
  model_type: transformer
  hidden_size: 256
  num_layers: 3
  num_heads: 8
  dropout: 0.1
  eval_freq: 5000
  eval_episodes: 10
  log_interval: 1000
  save_freq: 10000
  device: auto
  num_workers: 4
environment:
  initial_balance: 10000.0
  leverage: 1.0
  transaction_cost: 0.001
  slippage: 0.0001
  symbols:
  - BTC/USDT
  timeframe: 1h
  features:
  - close
  - volume
  - rsi
  - macd
  max_position_size: 1.0
  stop_loss: null
  take_profit: null
  reward_type: pnl
  reward_scaling: 1.0
  dataset_name: sample_dataset
  episode_length: 100
  lookback_window: 24
  train_start:
  - 50
  train_end:
  - 500
  test_start:
  - 500
  test_end:
  - 700
model:
  model_type: transformer
  input_size: 100
  hidden_size: 256
  output_size: 3
  num_layers: 2
  num_heads: 4
  dropout: 0.1
  activation: gelu
  learning_rate: 0.0003
  weight_decay: 1.0e-05
  gradient_clip: 1.0
  d_model: 128
  lookback_window: 24
  feature_dim: null
