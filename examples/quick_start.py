"""
Quick start example for RL-CT framework.

This script demonstrates how to:
1. Collect cryptocurrency data
2. Process and prepare the data
3. Train a reinforcement learning model
4. Evaluate the trained model
5. Run backtesting
"""

import os
import sys
from pathlib import Path
import numpy as np
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from rl_ct.utils.logger import setup_logging, get_logger
from rl_ct.utils.config import get_default_config, save_config
from rl_ct.scripts.collect_data import DataCollector
from rl_ct.scripts.train import train_single_run
from rl_ct.scripts.evaluate import ModelEvaluator
from rl_ct.scripts.backtest import Backtester

logger = get_logger(__name__)


def create_sample_data(output_dir: str = "data/raw") -> None:
    """
    Create sample cryptocurrency data for demonstration.
    
    Args:
        output_dir: Directory to save sample data
    """
    logger.info("Creating sample cryptocurrency data...")
    
    # Create sample data that mimics real cryptocurrency price movements
    np.random.seed(42)
    
    # Generate 30 days of hourly data (720 hours)
    n_hours = 720
    timestamps = pd.date_range('2023-01-01', periods=n_hours, freq='1h')
    
    # Generate realistic price movements using geometric Brownian motion
    initial_price = 30000.0  # Starting BTC price
    volatility = 0.02  # 2% hourly volatility
    drift = 0.0001  # Small positive drift
    
    # Generate price series
    returns = np.random.normal(drift, volatility, n_hours)
    prices = [initial_price]
    
    for i in range(1, n_hours):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(new_price)
    
    prices = np.array(prices)
    
    # Generate OHLCV data
    data = {
        'timestamp': timestamps,
        'open': prices,
        'high': prices * (1 + np.abs(np.random.normal(0, 0.005, n_hours))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.005, n_hours))),
        'close': prices,
        'volume': np.random.lognormal(10, 1, n_hours)  # Log-normal volume distribution
    }
    
    # Ensure high >= close >= low
    for i in range(n_hours):
        data['high'][i] = max(data['high'][i], data['close'][i])
        data['low'][i] = min(data['low'][i], data['close'][i])
    
    # Create DataFrame
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    
    # Save to CSV
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    csv_file = output_path / "BTC_USDT_sample_data.csv"
    df.to_csv(csv_file)
    
    logger.info(f"Sample data created: {csv_file}")
    logger.info(f"Data shape: {df.shape}")
    logger.info(f"Price range: ${df['close'].min():.2f} - ${df['close'].max():.2f}")


def process_sample_data() -> None:
    """Process the sample data for training."""
    logger.info("Processing sample data...")
    
    # Load sample data
    data_file = Path("data/raw/BTC_USDT_sample_data.csv")
    if not data_file.exists():
        raise FileNotFoundError(f"Sample data not found: {data_file}")
    
    df = pd.read_csv(data_file, index_col='timestamp', parse_dates=True)
    
    # Create data processor
    config = {
        'scaler': {
            'type': 'quantile',
            'min_quantile': 0.5,
            'max_quantile': 99.5,
            'scale_coef': 10000.0
        },
        'feature_engineering': {
            'create_technical_indicators': True,
            'create_time_features': True,
        },
        'lag_features': {
            'columns': ['close', 'volume'],
            'periods': [1, 2, 3]
        }
    }
    
    from rl_ct.utils.preprocessing import DataProcessor
    processor = DataProcessor(config)
    
    # Process data
    processed_df = processor.create_features(df)
    scaled_features = processor.scale_data(processed_df)
    
    # Extract price data
    processor.price_data = df[['open', 'high', 'low', 'close']].values
    
    # Save processed data
    processor.save_processed_data("data/processed", "sample_dataset")
    
    logger.info("Data processing completed")
    logger.info(f"Processed data shape: {scaled_features.shape}")


def create_training_config() -> str:
    """Create training configuration."""
    logger.info("Creating training configuration...")
    
    # Get default config and modify for quick demo
    config = get_default_config()
    
    # Modify for quick training
    config.training.total_timesteps = 50000  # Reduced for demo
    config.training.eval_freq = 5000
    config.training.save_freq = 10000
    config.training.log_interval = 1000
    config.training.lookback_window = 24
    
    # Environment settings
    config.environment.dataset_name = "sample_dataset"
    config.environment.episode_length = 100  # Shorter episodes
    config.environment.lookback_window = 24  # 24 hours lookback
    config.environment.train_start = [50]  # Skip initial data for stability
    config.environment.train_end = [500]
    config.environment.test_start = [500]
    config.environment.test_end = [700]
    
    # Model settings (smaller for demo)
    config.model.d_model = 128
    config.model.num_heads = 4
    config.model.num_layers = 2
    config.model.lookback_window = 24
    config.model.feature_dim = None  # Will be set automatically
    

    # Save config
    config_path = "configs/training/quick_start.yaml"
    Path(config_path).parent.mkdir(parents=True, exist_ok=True)
    save_config(config, config_path)
    
    logger.info(f"Training config saved: {config_path}")
    return config_path


def run_training(config_path: str) -> str:
    """Run model training."""
    logger.info("Starting model training...")
    
    try:
        best_checkpoint = train_single_run(
            config_path=config_path,
            experiment_name="quick_start_demo",
            resume=False
        )
        
        logger.info(f"Training completed. Best model: {best_checkpoint}")
        return best_checkpoint
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        # For demo purposes, create a dummy checkpoint path
        dummy_path = "checkpoints/quick_start_demo/dummy_model.zip"
        Path(dummy_path).parent.mkdir(parents=True, exist_ok=True)
        Path(dummy_path).touch()
        logger.warning(f"Using dummy checkpoint for demo: {dummy_path}")
        return dummy_path


def run_evaluation(model_path: str, config_path: str) -> None:
    """Run model evaluation."""
    logger.info("Running model evaluation...")
    
    try:
        # Create evaluator
        evaluator = ModelEvaluator(
            model_path=model_path,
            config_path="configs/environment/default.yaml"
        )
        
        # Run evaluation
        results = evaluator.evaluate_episodes(
            n_episodes=5,  # Reduced for demo
            deterministic=True
        )
        
        # Save results
        evaluator.save_results(results, "results/evaluation")
        evaluator.create_visualizations(results, "results/evaluation")
        
        logger.info("Evaluation completed")
        logger.info(f"Mean reward: {results['mean_reward']:.2f}")
        logger.info(f"Mean final balance: ${results['mean_final_balance']:.2f}")
        
        evaluator.close()
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        logger.info("Skipping evaluation for demo")


def run_backtest(model_path: str) -> None:
    """Run backtesting."""
    logger.info("Running backtesting...")
    
    try:
        # Create backtester
        backtester = Backtester(
            model_path=model_path,
            config_path="configs/environment/default.yaml",
            initial_balance=10000.0
        )
        
        # Run backtest
        results = backtester.run_backtest(deterministic=True)
        
        # Save results
        backtester.save_results(results, "results/backtest")
        backtester.create_visualizations(results, "results/backtest")
        
        logger.info("Backtesting completed")
        logger.info(f"Final balance: ${results['summary']['final_balance']:.2f}")
        logger.info(f"Total return: {results['summary']['total_return']:.2%}")
        
        backtester.close()
        
    except Exception as e:
        logger.error(f"Backtesting failed: {e}")
        logger.info("Skipping backtesting for demo")


def main():
    """Main function to run the complete demo."""
    # Setup logging
    setup_logging(level='INFO', log_dir='logs', log_file='quick_start.log')
    
    logger.info("=" * 60)
    logger.info("RL-CT Framework Quick Start Demo")
    logger.info("=" * 60)
    
    try:
        # Step 1: Create sample data
        create_sample_data()
        
        # Step 2: Process data
        process_sample_data()
        
        # Step 3: Create training configuration
        config_path = create_training_config()
        
        # Step 4: Train model
        model_path = run_training(config_path)
        
        # Step 5: Evaluate model
        run_evaluation(model_path, config_path)
        
        # Step 6: Run backtest
        run_backtest(model_path)
        
        logger.info("=" * 60)
        logger.info("Quick start demo completed successfully!")
        logger.info("=" * 60)
        logger.info("Next steps:")
        logger.info("1. Check results in 'results/' directory")
        logger.info("2. Modify configs for your specific use case")
        logger.info("3. Collect real market data using 'rl-ct collect-data'")
        logger.info("4. Train on larger datasets with 'rl-ct train'")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        logger.info("Please check the logs for more details")


if __name__ == "__main__":
    main()
