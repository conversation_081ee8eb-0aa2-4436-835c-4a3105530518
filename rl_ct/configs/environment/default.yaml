# Default environment configuration for cryptocurrency trading

# Basic trading parameters
initial_balance: 10000.0
leverage: 1.0
transaction_cost: 0.001  # 0.1% transaction cost
slippage: 0.0001  # 0.01% slippage
order_size: 100.0  # USD per order
maintenance_margin_rate: 0.01  # 1% maintenance margin

# Data configuration
dataset_name: "default"
data_dir: "data/processed"
file_format: "npy"
lookback_window: 168  # 1 week of hourly data
episode_length: 168   # 1 week episodes

# Training/evaluation data splits
train_start: [1000, 3000, 5000]
train_end: [2500, 4500, 6500]
test_start: [2500, 4500, 6500]
test_end: [3000, 5000, 7000]

# Environment behavior
regime: "training"  # training, evaluation, backtesting
max_steps: 1000
max_drawdown_threshold: 0.5  # 50% max drawdown before termination

# Reward configuration
reward_type: "pnl"  # pnl, sharpe, sortino
reward_scaling: 1.0

# Data scaling configuration
scaler:
  type: "quantile"  # quantile, minmax, standard
  min_quantile: 0.5
  max_quantile: 99.5
  scale_coef: 10000.0

# Feature engineering
feature_engineering:
  create_technical_indicators: true
  create_time_features: true
  create_lag_features: true

lag_features:
  columns: ["close", "volume", "rsi_14"]
  periods: [1, 2, 3, 5]

# Selected features (if null, use all features)
selected_features: null

# Risk management
risk_management:
  max_position_size: 1.0  # Maximum position as fraction of balance
  stop_loss: null  # Stop loss percentage (null to disable)
  take_profit: null  # Take profit percentage (null to disable)
  max_trades_per_episode: null  # Maximum trades per episode (null for unlimited)

# Logging and monitoring
logging:
  level: "INFO"
  log_trades: true
  log_rewards: true
  log_positions: true
