# Transformer model configuration

# Model type
type: "transformer"

# Architecture parameters
d_model: 256
num_heads: 8
d_ff: 512
num_encoder_layers: 3
num_decoder_layers: 0  # Encoder-only

# Input/output dimensions (will be set automatically based on environment)
input_size: null
output_size: null

# Sequence parameters
lookback_window: 168  # 1 week of hourly data
feature_dim: null  # Will be calculated from data
max_seq_length: 200
use_positional_encoding: true

# Regularization
dropout: 0.1
weight_decay: 1e-5
gradient_clip: 1.0

# Activation function
activation: "gelu"

# Training parameters
learning_rate: 3e-4
lr_schedule: "constant"  # constant, linear, exponential

# Model-specific optimizations
use_flash_attention: false  # Enable if available
use_gradient_checkpointing: false  # For memory efficiency

# Initialization
init_method: "xavier_uniform"  # xavier_uniform, xavier_normal, kaiming_uniform, kaiming_normal

# Advanced features
use_layer_scale: false
layer_scale_init: 1e-4
use_stochastic_depth: false
stochastic_depth_rate: 0.1

# Value function sharing
vf_share_layers: true  # Share encoder between policy and value function

# Model ensemble (for advanced usage)
ensemble:
  enabled: false
  num_models: 3
  voting_method: "average"  # average, majority

# Attention mechanism options
attention:
  attention_dropout: 0.1
  use_relative_position: false
  max_relative_position: 32
  use_talking_heads: false
  use_multi_query: false

# Feed-forward network options
feedforward:
  use_glu: false  # Gated Linear Unit
  use_swiglu: false  # SwiGLU activation
  expansion_factor: 4

# Normalization options
normalization:
  type: "layer_norm"  # layer_norm, rms_norm, batch_norm
  eps: 1e-6
  pre_norm: true  # Pre-normalization vs post-normalization

# Memory and efficiency
memory_efficient: false
use_checkpoint: false  # Gradient checkpointing
compile_model: false  # PyTorch 2.0 compilation
