"""
Command Line Interface for RL-CT framework.
"""

import typer
from typing import Op<PERSON>
from pathlib import Path
from rich.console import <PERSON>sole
from rich.table import Table

app = typer.Typer(
    name="rl-ct",
    help="Reinforcement Learning for Cryptocurrency Trading",
    add_completion=False,
)
console = Console()


@app.command()
def train(
    config_path: str = typer.Option(
        "configs/training/default.yaml",
        "--config", "-c",
        help="Path to training configuration file"
    ),
    resume: bool = typer.Option(
        False,
        "--resume", "-r",
        help="Resume training from checkpoint"
    ),
    checkpoint_path: Optional[str] = typer.Option(
        None,
        "--checkpoint",
        help="Path to checkpoint to resume from"
    ),
    experiment_name: Optional[str] = typer.Option(
        None,
        "--name", "-n",
        help="Experiment name"
    ),
):
    """Train a reinforcement learning agent."""
    console.print(f"[bold green]Starting training with config: {config_path}[/bold green]")
    
    # Import here to avoid circular imports
    from rl_ct.scripts.train import main as train_main
    
    train_main(
        config_path=config_path,
        resume=resume,
        checkpoint_path=checkpoint_path,
        experiment_name=experiment_name,
    )


@app.command()
def evaluate(
    model_path: str = typer.Argument(..., help="Path to trained model"),
    config_path: str = typer.Option(
        "configs/environment/default.yaml",
        "--config", "-c",
        help="Path to environment configuration file"
    ),
    output_dir: str = typer.Option(
        "results/evaluation",
        "--output", "-o",
        help="Output directory for evaluation results"
    ),
):
    """Evaluate a trained model."""
    console.print(f"[bold blue]Evaluating model: {model_path}[/bold blue]")
    
    from rl_ct.scripts.evaluate import main as eval_main
    
    eval_main(
        model_path=model_path,
        config_path=config_path,
        output_dir=output_dir,
    )


@app.command()
def backtest(
    model_path: str = typer.Argument(..., help="Path to trained model"),
    data_path: str = typer.Option(
        "data/processed/test_data.csv",
        "--data", "-d",
        help="Path to test data"
    ),
    output_dir: str = typer.Option(
        "results/backtest",
        "--output", "-o",
        help="Output directory for backtest results"
    ),
):
    """Run backtesting on historical data."""
    console.print(f"[bold yellow]Running backtest with model: {model_path}[/bold yellow]")
    
    from rl_ct.scripts.backtest import main as backtest_main
    
    backtest_main(
        model_path=model_path,
        data_path=data_path,
        output_dir=output_dir,
    )


@app.command()
def collect_data(
    symbols: str = typer.Option(
        "BTC/USDT,ETH/USDT",
        "--symbols", "-s",
        help="Comma-separated list of trading symbols"
    ),
    timeframe: str = typer.Option(
        "1h",
        "--timeframe", "-t",
        help="Timeframe for data collection"
    ),
    days: int = typer.Option(
        30,
        "--days", "-d",
        help="Number of days to collect"
    ),
    output_dir: str = typer.Option(
        "data/raw",
        "--output", "-o",
        help="Output directory for collected data"
    ),
):
    """Collect cryptocurrency market data."""
    console.print(f"[bold cyan]Collecting data for symbols: {symbols}[/bold cyan]")
    
    from rl_ct.scripts.collect_data import main as collect_main
    
    collect_main(
        symbols=symbols.split(","),
        timeframe=timeframe,
        days=days,
        output_dir=output_dir,
    )


@app.command()
def info():
    """Show framework information."""
    table = Table(title="RL-CT Framework Information")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Description")
    
    table.add_row("Environment", "✓", "Cryptocurrency trading environment")
    table.add_row("Models", "✓", "Transformer-based neural networks")
    table.add_row("Training", "✓", "Ray RLlib integration")
    table.add_row("Evaluation", "✓", "Backtesting and performance metrics")
    table.add_row("Data", "✓", "Multi-source data collection")
    
    console.print(table)


def main():
    """Main entry point for the CLI."""
    app()


if __name__ == "__main__":
    main()
