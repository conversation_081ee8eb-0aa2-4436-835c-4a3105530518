"""
Data scaling utilities for normalizing features.
"""

from abc import ABC, abstractmethod
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any
import numpy as np
from sklearn.preprocessing import MinMaxScaler as SKMinMaxScaler
from sklearn.preprocessing import StandardScaler as SKStandardScaler

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class Scaler(ABC):
    """Base class for data scalers."""
    
    def __init__(self):
        self.is_fitted = False
        
    @abstractmethod
    def fit(self, data: np.ndarray) -> 'Scaler':
        """
        Fit the scaler to the data.
        
        Args:
            data: Data to fit the scaler on
            
        Returns:
            Self for method chaining
        """
        pass
        
    @abstractmethod
    def transform(self, data: np.ndarray) -> np.ndarray:
        """
        Transform the data using the fitted scaler.
        
        Args:
            data: Data to transform
            
        Returns:
            Transformed data
        """
        pass
        
    @abstractmethod
    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """
        Inverse transform the scaled data.
        
        Args:
            data: Scaled data to inverse transform
            
        Returns:
            Original scale data
        """
        pass
        
    def fit_transform(self, data: np.ndarray) -> np.ndarray:
        """
        Fit the scaler and transform the data.
        
        Args:
            data: Data to fit and transform
            
        Returns:
            Transformed data
        """
        return self.fit(data).transform(data)
        
    def reset(self, data: np.ndarray, reset_data: np.ndarray) -> np.ndarray:
        """
        Reset the scaler with new data and transform.
        
        Args:
            data: Current data to transform
            reset_data: Data to refit the scaler on
            
        Returns:
            Transformed current data
        """
        self.fit(reset_data)
        return self.transform(data)
        
    def step(self, data: np.ndarray) -> np.ndarray:
        """
        Transform data in a single step (assumes scaler is already fitted).
        
        Args:
            data: Data to transform
            
        Returns:
            Transformed data
        """
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before calling step()")
        return self.transform(data)


class MinMaxScaler(Scaler):
    """Min-Max scaler that scales features to a given range."""
    
    def __init__(self, feature_range: Tuple[float, float] = (0, 1)):
        """
        Initialize MinMax scaler.
        
        Args:
            feature_range: Desired range of transformed data
        """
        super().__init__()
        self.feature_range = feature_range
        self.scaler = SKMinMaxScaler(feature_range=feature_range)
        
    def fit(self, data: np.ndarray) -> 'MinMaxScaler':
        """Fit the MinMax scaler."""
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        self.scaler.fit(data)
        self.is_fitted = True
        logger.debug(f"MinMaxScaler fitted with data shape: {data.shape}")
        return self
        
    def transform(self, data: np.ndarray) -> np.ndarray:
        """Transform data using MinMax scaling."""
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        transformed = self.scaler.transform(data)
        
        if len(original_shape) == 1:
            transformed = transformed.flatten()
            
        return transformed
        
    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """Inverse transform MinMax scaled data."""
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        inverse_transformed = self.scaler.inverse_transform(data)
        
        if len(original_shape) == 1:
            inverse_transformed = inverse_transformed.flatten()
            
        return inverse_transformed


class StandardScaler(Scaler):
    """Standard scaler that standardizes features by removing mean and scaling to unit variance."""
    
    def __init__(self):
        """Initialize Standard scaler."""
        super().__init__()
        self.scaler = SKStandardScaler()
        
    def fit(self, data: np.ndarray) -> 'StandardScaler':
        """Fit the Standard scaler."""
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        self.scaler.fit(data)
        self.is_fitted = True
        logger.debug(f"StandardScaler fitted with data shape: {data.shape}")
        return self
        
    def transform(self, data: np.ndarray) -> np.ndarray:
        """Transform data using Standard scaling."""
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        transformed = self.scaler.transform(data)
        
        if len(original_shape) == 1:
            transformed = transformed.flatten()
            
        return transformed
        
    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """Inverse transform Standard scaled data."""
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        inverse_transformed = self.scaler.inverse_transform(data)
        
        if len(original_shape) == 1:
            inverse_transformed = inverse_transformed.flatten()
            
        return inverse_transformed


class QuantileScaler(Scaler):
    """Custom quantile-based scaler similar to the reference implementation."""
    
    def __init__(
        self,
        min_quantile: float = 0.5,
        max_quantile: float = 99.5,
        scale_coef: float = 1000.0
    ):
        """
        Initialize Quantile scaler.
        
        Args:
            min_quantile: Lower quantile for clipping
            max_quantile: Upper quantile for clipping
            scale_coef: Scaling coefficient
        """
        super().__init__()
        self.min_quantile = min_quantile
        self.max_quantile = max_quantile
        self.scale_coef = scale_coef
        
        # Fitted parameters
        self.min_vals: Optional[np.ndarray] = None
        self.max_vals: Optional[np.ndarray] = None
        
    def fit(self, data: np.ndarray) -> 'QuantileScaler':
        """Fit the Quantile scaler."""
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        # Calculate quantiles
        self.min_vals = np.percentile(data, self.min_quantile, axis=0)
        self.max_vals = np.percentile(data, self.max_quantile, axis=0)
        
        self.is_fitted = True
        logger.debug(f"QuantileScaler fitted with data shape: {data.shape}")
        return self
        
    def transform(self, data: np.ndarray) -> np.ndarray:
        """Transform data using Quantile scaling."""
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before transform")
            
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        # Clip values to quantile range
        clipped = np.clip(data, self.min_vals, self.max_vals)
        
        # Scale to [0, 1] range
        range_vals = self.max_vals - self.min_vals
        range_vals = np.where(range_vals == 0, 1, range_vals)  # Avoid division by zero
        
        scaled = (clipped - self.min_vals) / range_vals
        
        # Apply scaling coefficient
        scaled = scaled / self.scale_coef
        
        if len(original_shape) == 1:
            scaled = scaled.flatten()
            
        return scaled

    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """Inverse transform Quantile scaled data."""
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before inverse_transform")

        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)

        # Reverse scaling coefficient
        unscaled = data * self.scale_coef

        # Reverse normalization
        range_vals = self.max_vals - self.min_vals
        range_vals = np.where(range_vals == 0, 1, range_vals)

        inverse_transformed = unscaled * range_vals + self.min_vals

        if len(original_shape) == 1:
            inverse_transformed = inverse_transformed.flatten()

        return inverse_transformed

    def fit_transform(self, data: np.ndarray) -> np.ndarray:
        """Fit and transform data."""
        return self.fit(data).transform(data)


class SegmentedScaler(Scaler):
    """Scaler that handles different data segments separately."""

    def __init__(
        self,
        d_time: int = 2,
        d_account: int = 2,
        d_technical: int = 96,
        time_scaler_type: str = "none",  # Time is already normalized
        account_scaler_type: str = "quantile",
        technical_scaler_type: str = "quantile",
        **scaler_kwargs
    ):
        """
        Initialize segmented scaler.

        Args:
            d_time: Time dimension
            d_account: Account dimension
            d_technical: Technical indicators dimension
            time_scaler_type: Scaler type for time data
            account_scaler_type: Scaler type for account data
            technical_scaler_type: Scaler type for technical data
            **scaler_kwargs: Additional arguments for scalers
        """
        super().__init__()
        self.d_time = d_time
        self.d_account = d_account
        self.d_technical = d_technical
        self.d_obs = d_time + d_account + d_technical

        # Create scalers for each segment
        self.time_scaler = self._create_scaler(time_scaler_type, **scaler_kwargs)
        self.account_scaler = self._create_scaler(account_scaler_type, **scaler_kwargs)
        self.technical_scaler = self._create_scaler(technical_scaler_type, **scaler_kwargs)

    def _create_scaler(self, scaler_type: str, **kwargs) -> Optional[Scaler]:
        """Create scaler based on type."""
        if scaler_type == "none":
            return None
        elif scaler_type == "minmax":
            return MinMaxScaler(**kwargs)
        elif scaler_type == "standard":
            return StandardScaler(**kwargs)
        elif scaler_type == "quantile":
            return QuantileScaler(**kwargs)
        else:
            raise ValueError(f"Unknown scaler type: {scaler_type}")

    def fit(self, data: np.ndarray) -> 'SegmentedScaler':
        """
        Fit scalers to segmented data.

        Args:
            data: Data array [n_samples, seq_len, d_obs] or [n_samples, seq_len * d_obs]

        Returns:
            Self for method chaining
        """
        # Reshape if needed
        if data.ndim == 2:
            n_samples = data.shape[0]
            seq_len = data.shape[1] // self.d_obs
            data = data.reshape(n_samples, seq_len, self.d_obs)

        # Split data into segments
        time_data = data[:, :, :self.d_time]
        account_data = data[:, :, self.d_time:self.d_time + self.d_account]
        technical_data = data[:, :, self.d_time + self.d_account:]

        # Fit scalers
        if self.time_scaler is not None:
            self.time_scaler.fit(time_data.reshape(-1, self.d_time))

        if self.account_scaler is not None:
            self.account_scaler.fit(account_data.reshape(-1, self.d_account))

        if self.technical_scaler is not None:
            self.technical_scaler.fit(technical_data.reshape(-1, self.d_technical))

        self.is_fitted = True
        logger.debug("SegmentedScaler fitted successfully")
        return self

    def transform(self, data: np.ndarray) -> np.ndarray:
        """
        Transform segmented data.

        Args:
            data: Data to transform

        Returns:
            Transformed data
        """
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before transform")

        original_shape = data.shape

        # Reshape if needed
        if data.ndim == 2:
            n_samples = data.shape[0]
            seq_len = data.shape[1] // self.d_obs
            data = data.reshape(n_samples, seq_len, self.d_obs)

        # Split data into segments
        time_data = data[:, :, :self.d_time]
        account_data = data[:, :, self.d_time:self.d_time + self.d_account]
        technical_data = data[:, :, self.d_time + self.d_account:]

        # Transform each segment
        if self.time_scaler is not None:
            time_shape = time_data.shape
            time_data = self.time_scaler.transform(time_data.reshape(-1, self.d_time))
            time_data = time_data.reshape(time_shape)

        if self.account_scaler is not None:
            account_shape = account_data.shape
            account_data = self.account_scaler.transform(account_data.reshape(-1, self.d_account))
            account_data = account_data.reshape(account_shape)

        if self.technical_scaler is not None:
            technical_shape = technical_data.shape
            technical_data = self.technical_scaler.transform(technical_data.reshape(-1, self.d_technical))
            technical_data = technical_data.reshape(technical_shape)

        # Recombine segments
        transformed_data = np.concatenate([time_data, account_data, technical_data], axis=-1)

        # Restore original shape if needed
        if len(original_shape) == 2:
            transformed_data = transformed_data.reshape(original_shape)

        return transformed_data

    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """
        Inverse transform segmented data.

        Args:
            data: Transformed data to inverse transform

        Returns:
            Original scale data
        """
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before inverse_transform")

        original_shape = data.shape

        # Reshape if needed
        if data.ndim == 2:
            n_samples = data.shape[0]
            seq_len = data.shape[1] // self.d_obs
            data = data.reshape(n_samples, seq_len, self.d_obs)

        # Split data into segments
        time_data = data[:, :, :self.d_time]
        account_data = data[:, :, self.d_time:self.d_time + self.d_account]
        technical_data = data[:, :, self.d_time + self.d_account:]

        # Inverse transform each segment
        if self.time_scaler is not None:
            time_shape = time_data.shape
            time_data = self.time_scaler.inverse_transform(time_data.reshape(-1, self.d_time))
            time_data = time_data.reshape(time_shape)

        if self.account_scaler is not None:
            account_shape = account_data.shape
            account_data = self.account_scaler.inverse_transform(account_data.reshape(-1, self.d_account))
            account_data = account_data.reshape(account_shape)

        if self.technical_scaler is not None:
            technical_shape = technical_data.shape
            technical_data = self.technical_scaler.inverse_transform(technical_data.reshape(-1, self.d_technical))
            technical_data = technical_data.reshape(technical_shape)

        # Recombine segments
        original_data = np.concatenate([time_data, account_data, technical_data], axis=-1)

        # Restore original shape if needed
        if len(original_shape) == 2:
            original_data = original_data.reshape(original_shape)

        return original_data

    def fit_transform(self, data: np.ndarray) -> np.ndarray:
        """Fit and transform data."""
        return self.fit(data).transform(data)

    def reset(self, state_array: np.ndarray, reset_array: np.ndarray) -> np.ndarray:
        """Reset method for compatibility with existing code."""
        # Fit on reset array and transform state array
        self.fit(reset_array)
        return self.transform(state_array)

    def step(self, obs_step: np.ndarray) -> np.ndarray:
        """Step method for compatibility with existing code."""
        return self.transform(obs_step)
