"""
Data scaling utilities for normalizing features.
"""

from abc import ABC, abstractmethod
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any
import numpy as np
from sklearn.preprocessing import MinMaxScaler as SKMinMaxScaler
from sklearn.preprocessing import StandardScaler as SKStandardScaler

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class Scaler(ABC):
    """Base class for data scalers."""
    
    def __init__(self):
        self.is_fitted = False
        
    @abstractmethod
    def fit(self, data: np.ndarray) -> 'Scaler':
        """
        Fit the scaler to the data.
        
        Args:
            data: Data to fit the scaler on
            
        Returns:
            Self for method chaining
        """
        pass
        
    @abstractmethod
    def transform(self, data: np.ndarray) -> np.ndarray:
        """
        Transform the data using the fitted scaler.
        
        Args:
            data: Data to transform
            
        Returns:
            Transformed data
        """
        pass
        
    @abstractmethod
    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """
        Inverse transform the scaled data.
        
        Args:
            data: Scaled data to inverse transform
            
        Returns:
            Original scale data
        """
        pass
        
    def fit_transform(self, data: np.ndarray) -> np.ndarray:
        """
        Fit the scaler and transform the data.
        
        Args:
            data: Data to fit and transform
            
        Returns:
            Transformed data
        """
        return self.fit(data).transform(data)
        
    def reset(self, data: np.ndarray, reset_data: np.ndarray) -> np.ndarray:
        """
        Reset the scaler with new data and transform.
        
        Args:
            data: Current data to transform
            reset_data: Data to refit the scaler on
            
        Returns:
            Transformed current data
        """
        self.fit(reset_data)
        return self.transform(data)
        
    def step(self, data: np.ndarray) -> np.ndarray:
        """
        Transform data in a single step (assumes scaler is already fitted).
        
        Args:
            data: Data to transform
            
        Returns:
            Transformed data
        """
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before calling step()")
        return self.transform(data)


class MinMaxScaler(Scaler):
    """Min-Max scaler that scales features to a given range."""
    
    def __init__(self, feature_range: Tuple[float, float] = (0, 1)):
        """
        Initialize MinMax scaler.
        
        Args:
            feature_range: Desired range of transformed data
        """
        super().__init__()
        self.feature_range = feature_range
        self.scaler = SKMinMaxScaler(feature_range=feature_range)
        
    def fit(self, data: np.ndarray) -> 'MinMaxScaler':
        """Fit the MinMax scaler."""
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        self.scaler.fit(data)
        self.is_fitted = True
        logger.debug(f"MinMaxScaler fitted with data shape: {data.shape}")
        return self
        
    def transform(self, data: np.ndarray) -> np.ndarray:
        """Transform data using MinMax scaling."""
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        transformed = self.scaler.transform(data)
        
        if len(original_shape) == 1:
            transformed = transformed.flatten()
            
        return transformed
        
    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """Inverse transform MinMax scaled data."""
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        inverse_transformed = self.scaler.inverse_transform(data)
        
        if len(original_shape) == 1:
            inverse_transformed = inverse_transformed.flatten()
            
        return inverse_transformed


class StandardScaler(Scaler):
    """Standard scaler that standardizes features by removing mean and scaling to unit variance."""
    
    def __init__(self):
        """Initialize Standard scaler."""
        super().__init__()
        self.scaler = SKStandardScaler()
        
    def fit(self, data: np.ndarray) -> 'StandardScaler':
        """Fit the Standard scaler."""
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        self.scaler.fit(data)
        self.is_fitted = True
        logger.debug(f"StandardScaler fitted with data shape: {data.shape}")
        return self
        
    def transform(self, data: np.ndarray) -> np.ndarray:
        """Transform data using Standard scaling."""
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        transformed = self.scaler.transform(data)
        
        if len(original_shape) == 1:
            transformed = transformed.flatten()
            
        return transformed
        
    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """Inverse transform Standard scaled data."""
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        inverse_transformed = self.scaler.inverse_transform(data)
        
        if len(original_shape) == 1:
            inverse_transformed = inverse_transformed.flatten()
            
        return inverse_transformed


class QuantileScaler(Scaler):
    """Custom quantile-based scaler similar to the reference implementation."""
    
    def __init__(
        self,
        min_quantile: float = 0.5,
        max_quantile: float = 99.5,
        scale_coef: float = 1000.0
    ):
        """
        Initialize Quantile scaler.
        
        Args:
            min_quantile: Lower quantile for clipping
            max_quantile: Upper quantile for clipping
            scale_coef: Scaling coefficient
        """
        super().__init__()
        self.min_quantile = min_quantile
        self.max_quantile = max_quantile
        self.scale_coef = scale_coef
        
        # Fitted parameters
        self.min_vals: Optional[np.ndarray] = None
        self.max_vals: Optional[np.ndarray] = None
        
    def fit(self, data: np.ndarray) -> 'QuantileScaler':
        """Fit the Quantile scaler."""
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        # Calculate quantiles
        self.min_vals = np.percentile(data, self.min_quantile, axis=0)
        self.max_vals = np.percentile(data, self.max_quantile, axis=0)
        
        self.is_fitted = True
        logger.debug(f"QuantileScaler fitted with data shape: {data.shape}")
        return self
        
    def transform(self, data: np.ndarray) -> np.ndarray:
        """Transform data using Quantile scaling."""
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before transform")
            
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        # Clip values to quantile range
        clipped = np.clip(data, self.min_vals, self.max_vals)
        
        # Scale to [0, 1] range
        range_vals = self.max_vals - self.min_vals
        range_vals = np.where(range_vals == 0, 1, range_vals)  # Avoid division by zero
        
        scaled = (clipped - self.min_vals) / range_vals
        
        # Apply scaling coefficient
        scaled = scaled / self.scale_coef
        
        if len(original_shape) == 1:
            scaled = scaled.flatten()
            
        return scaled
        
    def inverse_transform(self, data: np.ndarray) -> np.ndarray:
        """Inverse transform Quantile scaled data."""
        if not self.is_fitted:
            raise ValueError("Scaler must be fitted before inverse_transform")
            
        original_shape = data.shape
        if data.ndim == 1:
            data = data.reshape(-1, 1)
            
        # Reverse scaling coefficient
        unscaled = data * self.scale_coef
        
        # Reverse normalization
        range_vals = self.max_vals - self.min_vals
        range_vals = np.where(range_vals == 0, 1, range_vals)
        
        inverse_transformed = unscaled * range_vals + self.min_vals
        
        if len(original_shape) == 1:
            inverse_transformed = inverse_transformed.flatten()
            
        return inverse_transformed
