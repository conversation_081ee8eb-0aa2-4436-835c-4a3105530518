"""
Main data processing pipeline that combines all preprocessing steps.
"""

from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import pandas as pd
from pathlib import Path

from rl_ct.utils.data_loaders import DiskDataLoader, MarketDataLoader
from rl_ct.utils.preprocessing.scaler import Scaler, MinMaxScaler, StandardScaler, QuantileScaler
from rl_ct.utils.preprocessing.feature_engineer import FeatureEngineer
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class DataProcessor:
    """Main data processing pipeline for cryptocurrency trading data."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize data processor.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Initialize components
        self.feature_engineer = FeatureEngineer(config.get('feature_engineering', {}))
        self.scaler = self._init_scaler()
        
        # Data storage
        self.raw_data: Optional[pd.DataFrame] = None
        self.processed_data: Optional[pd.DataFrame] = None
        self.feature_data: Optional[np.ndarray] = None
        self.price_data: Optional[np.ndarray] = None
        
    def _init_scaler(self) -> Scaler:
        """Initialize the data scaler based on configuration."""
        scaler_type = self.config.get('scaler', {}).get('type', 'quantile')
        
        if scaler_type == 'minmax':
            feature_range = self.config.get('scaler', {}).get('feature_range', (0, 1))
            return MinMaxScaler(feature_range=feature_range)
        elif scaler_type == 'standard':
            return StandardScaler()
        elif scaler_type == 'quantile':
            min_quantile = self.config.get('scaler', {}).get('min_quantile', 0.5)
            max_quantile = self.config.get('scaler', {}).get('max_quantile', 99.5)
            scale_coef = self.config.get('scaler', {}).get('scale_coef', 1000.0)
            return QuantileScaler(min_quantile, max_quantile, scale_coef)
        else:
            raise ValueError(f"Unknown scaler type: {scaler_type}")
    
    def load_data_from_disk(self, data_dir: str, dataset_name: str) -> pd.DataFrame:
        """
        Load data from disk using DiskDataLoader.
        
        Args:
            data_dir: Directory containing data files
            dataset_name: Name of the dataset
            
        Returns:
            Loaded DataFrame
        """
        logger.info(f"Loading data from disk: {dataset_name}")
        
        loader = DiskDataLoader(
            data_dir=data_dir,
            dataset_name=dataset_name,
            file_format=self.config.get('file_format', 'csv')
        )
        
        price_data, feature_data = loader.load_data()
        
        # Convert to DataFrame if needed
        if isinstance(price_data, np.ndarray) and price_data.size > 0:
            # Assume price data has OHLCV columns
            columns = ['open', 'high', 'low', 'close', 'volume']
            if price_data.shape[1] >= len(columns):
                df = pd.DataFrame(price_data[:, :len(columns)], columns=columns)
            else:
                df = pd.DataFrame(price_data)
        else:
            df = pd.DataFrame()
            
        self.raw_data = df
        logger.info(f"Loaded {len(df)} rows of data")
        
        return df
    
    def load_data_from_market(
        self,
        exchange: str = "binance",
        symbols: List[str] = None,
        timeframe: str = "1h",
        limit: int = 1000
    ) -> pd.DataFrame:
        """
        Load data from market using MarketDataLoader.
        
        Args:
            exchange: Exchange name
            symbols: List of trading symbols
            timeframe: Timeframe for data
            limit: Maximum number of candles
            
        Returns:
            Loaded DataFrame
        """
        logger.info(f"Loading data from market: {exchange}")
        
        loader = MarketDataLoader(
            exchange=exchange,
            symbols=symbols or ["BTC/USDT"],
            timeframe=timeframe,
            limit=limit,
            config=self.config.get('market_data', {})
        )
        
        price_data, feature_data = loader.load_data()
        
        # Use the first symbol's data for now
        if loader.market_data:
            symbol = list(loader.market_data.keys())[0]
            df = loader.market_data[symbol]
        else:
            df = pd.DataFrame()
            
        self.raw_data = df
        logger.info(f"Loaded {len(df)} rows of market data")
        
        return df
    
    def create_features(self, df: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Create features from raw data.
        
        Args:
            df: Input DataFrame (uses self.raw_data if None)
            
        Returns:
            DataFrame with features
        """
        if df is None:
            df = self.raw_data
            
        if df is None or df.empty:
            raise ValueError("No data available for feature creation")
            
        logger.info("Creating features")
        
        # Create technical indicators
        df_with_features = self.feature_engineer.create_technical_indicators(df)
        
        # Create time features if datetime index
        if isinstance(df_with_features.index, pd.DatetimeIndex):
            df_with_features = self.feature_engineer.create_time_features(df_with_features)
        
        # Create lag features for selected columns
        lag_columns = self.config.get('lag_features', {}).get('columns', ['close', 'volume'])
        lag_periods = self.config.get('lag_features', {}).get('periods', [1, 2, 3])
        
        if lag_columns and lag_periods:
            df_with_features = self.feature_engineer.create_lag_features(
                df_with_features, lag_columns, lag_periods
            )
        
        # Select specific features if configured
        feature_list = self.config.get('selected_features')
        if feature_list:
            df_with_features = self.feature_engineer.select_features(df_with_features, feature_list)
        
        self.processed_data = df_with_features
        logger.info(f"Created {len(df_with_features.columns)} features")
        
        return df_with_features
    
    def scale_data(self, df: Optional[pd.DataFrame] = None) -> np.ndarray:
        """
        Scale the processed data.
        
        Args:
            df: Input DataFrame (uses self.processed_data if None)
            
        Returns:
            Scaled data array
        """
        if df is None:
            df = self.processed_data
            
        if df is None or df.empty:
            raise ValueError("No processed data available for scaling")
            
        logger.info("Scaling data")
        
        # Remove non-numeric columns
        numeric_df = df.select_dtypes(include=[np.number])
        
        # Handle missing values
        numeric_df = numeric_df.ffill().bfill().fillna(0)
        
        # Convert to numpy array
        data_array = numeric_df.values
        
        # Fit and transform the scaler
        scaled_data = self.scaler.fit_transform(data_array)
        
        self.feature_data = scaled_data
        logger.info(f"Scaled data shape: {scaled_data.shape}")
        
        return scaled_data
    
    def process_pipeline(
        self,
        data_source: str = "disk",
        **kwargs
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Run the complete data processing pipeline.
        
        Args:
            data_source: Source of data ('disk' or 'market')
            **kwargs: Additional arguments for data loading
            
        Returns:
            Tuple of (price_data, feature_data)
        """
        logger.info("Starting data processing pipeline")
        
        # Load data
        if data_source == "disk":
            df = self.load_data_from_disk(**kwargs)
        elif data_source == "market":
            df = self.load_data_from_market(**kwargs)
        else:
            raise ValueError(f"Unknown data source: {data_source}")
        
        if df.empty:
            raise ValueError("No data loaded")
        
        # Create features
        df_with_features = self.create_features(df)
        
        # Scale data
        scaled_features = self.scale_data(df_with_features)
        
        # Extract price data (OHLC)
        price_columns = ['open', 'high', 'low', 'close']
        available_price_columns = [col for col in price_columns if col in df.columns]
        
        if available_price_columns:
            price_data = df[available_price_columns].values
        else:
            # Use close price if OHLC not available
            if 'close' in df.columns:
                price_data = df[['close']].values
            else:
                price_data = np.array([])
        
        self.price_data = price_data
        
        logger.info("Data processing pipeline completed")
        logger.info(f"Price data shape: {price_data.shape}")
        logger.info(f"Feature data shape: {scaled_features.shape}")
        
        return price_data, scaled_features
    
    def save_processed_data(self, output_dir: str, dataset_name: str) -> None:
        """
        Save processed data to disk.
        
        Args:
            output_dir: Output directory
            dataset_name: Name for the dataset
        """
        if self.price_data is None or self.feature_data is None:
            raise ValueError("No processed data to save")
        
        logger.info(f"Saving processed data to {output_dir}/{dataset_name}")
        
        # Use DiskDataLoader to save
        loader = DiskDataLoader(
            data_dir=output_dir,
            dataset_name=dataset_name,
            file_format=self.config.get('output_format', 'npy')
        )
        
        # Create metadata
        metadata = {
            "price_data_shape": self.price_data.shape,
            "feature_data_shape": self.feature_data.shape,
            "scaler_type": type(self.scaler).__name__,
            "config": self.config
        }
        
        loader.save_data(self.price_data, self.feature_data, metadata)
        
        logger.info("Processed data saved successfully")
    
    def get_data_info(self) -> Dict[str, Any]:
        """
        Get information about the processed data.
        
        Returns:
            Dictionary with data information
        """
        info = {
            "config": self.config,
            "scaler_type": type(self.scaler).__name__,
        }
        
        if self.raw_data is not None:
            info["raw_data_shape"] = self.raw_data.shape
            info["raw_data_columns"] = list(self.raw_data.columns)
        
        if self.processed_data is not None:
            info["processed_data_shape"] = self.processed_data.shape
            info["processed_data_columns"] = list(self.processed_data.columns)
        
        if self.price_data is not None:
            info["price_data_shape"] = self.price_data.shape
        
        if self.feature_data is not None:
            info["feature_data_shape"] = self.feature_data.shape
        
        return info
