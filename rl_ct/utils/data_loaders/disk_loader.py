"""
Disk-based data loader for loading preprocessed data from files.
"""

import os
from typing import Dict, Any, Tu<PERSON>, Optional, List
import numpy as np
import pandas as pd
from pathlib import Path

from rl_ct.utils.data_loaders.base_loader import BaseDataLoader
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class DiskDataLoader(BaseDataLoader):
    """Load data from disk files (CSV, NPY, etc.)."""
    
    def __init__(
        self,
        data_dir: str = "data/processed",
        dataset_name: str = "default",
        file_format: str = "npy",
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize disk data loader.
        
        Args:
            data_dir: Directory containing data files
            dataset_name: Name of the dataset
            file_format: Format of data files ('npy', 'csv', 'parquet')
            config: Additional configuration
        """
        super().__init__(config)
        self.data_dir = Path(data_dir)
        self.dataset_name = dataset_name
        self.file_format = file_format.lower()
        
        # Data storage
        self.price_data: Optional[np.ndarray] = None
        self.feature_data: Optional[np.ndarray] = None
        self.metadata: Dict[str, Any] = {}
        
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Load data from disk files.
        
        Returns:
            Tuple of (price_data, feature_data)
        """
        logger.info(f"Loading dataset '{self.dataset_name}' from {self.data_dir}")
        
        # Construct file paths
        price_file = self.data_dir / self.dataset_name / f"prices.{self.file_format}"
        feature_file = self.data_dir / self.dataset_name / f"features.{self.file_format}"
        
        # Load price data
        if price_file.exists():
            self.price_data = self._load_file(price_file)
            logger.info(f"Loaded price data: {self.price_data.shape}")
        else:
            logger.warning(f"Price file not found: {price_file}")
            self.price_data = np.array([])
            
        # Load feature data
        if feature_file.exists():
            self.feature_data = self._load_file(feature_file)
            logger.info(f"Loaded feature data: {self.feature_data.shape}")
        else:
            logger.warning(f"Feature file not found: {feature_file}")
            self.feature_data = np.array([])
            
        # Load metadata if available
        metadata_file = self.data_dir / self.dataset_name / "metadata.json"
        if metadata_file.exists():
            import json
            with open(metadata_file, 'r') as f:
                self.metadata = json.load(f)
                
        # Validate loaded data
        if not self.validate_data(self.price_data):
            logger.error("Invalid price data")
            
        if not self.validate_data(self.feature_data):
            logger.error("Invalid feature data")
            
        return self.price_data, self.feature_data
        
    def _load_file(self, file_path: Path) -> np.ndarray:
        """
        Load data from a single file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Loaded data array
        """
        if self.file_format == "npy":
            return np.load(file_path)
        elif self.file_format == "csv":
            df = pd.read_csv(file_path)
            return df.values
        elif self.file_format == "parquet":
            df = pd.read_parquet(file_path)
            return df.values
        else:
            raise ValueError(f"Unsupported file format: {self.file_format}")
            
    def get_data_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded data.
        
        Returns:
            Dictionary containing data information
        """
        info = {
            "dataset_name": self.dataset_name,
            "data_dir": str(self.data_dir),
            "file_format": self.file_format,
            "metadata": self.metadata,
        }
        
        if self.price_data is not None:
            info["price_data_shape"] = self.price_data.shape
            info["price_data_dtype"] = str(self.price_data.dtype)
            
        if self.feature_data is not None:
            info["feature_data_shape"] = self.feature_data.shape
            info["feature_data_dtype"] = str(self.feature_data.dtype)
            
        return info
        
    def save_data(
        self,
        price_data: np.ndarray,
        feature_data: np.ndarray,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Save data to disk.
        
        Args:
            price_data: Price data array
            feature_data: Feature data array
            metadata: Optional metadata dictionary
        """
        # Create directory if it doesn't exist
        dataset_dir = self.data_dir / self.dataset_name
        dataset_dir.mkdir(parents=True, exist_ok=True)
        
        # Save price data
        price_file = dataset_dir / f"prices.{self.file_format}"
        self._save_file(price_data, price_file)
        
        # Save feature data
        feature_file = dataset_dir / f"features.{self.file_format}"
        self._save_file(feature_data, feature_file)
        
        # Save metadata
        if metadata:
            metadata_file = dataset_dir / "metadata.json"
            import json
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
                
        logger.info(f"Saved dataset '{self.dataset_name}' to {dataset_dir}")
        
    def _save_file(self, data: np.ndarray, file_path: Path) -> None:
        """
        Save data to a single file.
        
        Args:
            data: Data array to save
            file_path: Path to save the file
        """
        if self.file_format == "npy":
            np.save(file_path, data)
        elif self.file_format == "csv":
            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False)
        elif self.file_format == "parquet":
            df = pd.DataFrame(data)
            df.to_parquet(file_path, index=False)
        else:
            raise ValueError(f"Unsupported file format: {self.file_format}")
            
    def list_datasets(self) -> List[str]:
        """
        List available datasets in the data directory.
        
        Returns:
            List of dataset names
        """
        if not self.data_dir.exists():
            return []
            
        datasets = []
        for item in self.data_dir.iterdir():
            if item.is_dir():
                # Check if it contains data files
                price_file = item / f"prices.{self.file_format}"
                feature_file = item / f"features.{self.file_format}"
                if price_file.exists() or feature_file.exists():
                    datasets.append(item.name)
                    
        return sorted(datasets)
