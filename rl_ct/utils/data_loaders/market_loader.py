"""
Market data loader for fetching real-time and historical cryptocurrency data.
"""

import time
from datetime import datetime, timedelta
from typing import Dict, Any, Tu<PERSON>, Optional, List
import numpy as np
import pandas as pd
import ccxt
import yfinance as yf

from rl_ct.utils.data_loaders.base_loader import BaseDataLoader
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class MarketDataLoader(BaseDataLoader):
    """Load market data from various exchanges and data providers."""
    
    def __init__(
        self,
        exchange: str = "binance",
        symbols: List[str] = None,
        timeframe: str = "1h",
        limit: int = 1000,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize market data loader.
        
        Args:
            exchange: Exchange name (binance, coinbase, etc.)
            symbols: List of trading symbols
            timeframe: Timeframe for data ('1m', '5m', '1h', '1d', etc.)
            limit: Maximum number of candles to fetch
            config: Additional configuration
        """
        super().__init__(config)
        self.exchange_name = exchange
        self.symbols = symbols or ["BTC/USDT", "ETH/USDT"]
        self.timeframe = timeframe
        self.limit = limit
        
        # Initialize exchange
        self.exchange = self._init_exchange()
        
        # Data storage
        self.market_data: Dict[str, pd.DataFrame] = {}
        
    def _init_exchange(self) -> ccxt.Exchange:
        """
        Initialize the exchange connection.
        
        Returns:
            CCXT exchange instance
        """
        try:
            exchange_class = getattr(ccxt, self.exchange_name)
            exchange = exchange_class({
                'apiKey': self.config.get('api_key'),
                'secret': self.config.get('api_secret'),
                'password': self.config.get('passphrase'),  # For some exchanges
                'sandbox': self.config.get('sandbox', False),
                'enableRateLimit': True,
            })
            
            # Test connection
            exchange.load_markets()
            logger.info(f"Connected to {self.exchange_name} exchange")
            
            return exchange
            
        except Exception as e:
            logger.error(f"Failed to connect to {self.exchange_name}: {e}")
            raise
            
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Load market data from the exchange.
        
        Returns:
            Tuple of (price_data, feature_data)
        """
        logger.info(f"Fetching market data for {len(self.symbols)} symbols")
        
        all_price_data = []
        all_feature_data = []
        
        for symbol in self.symbols:
            try:
                # Fetch OHLCV data
                ohlcv = self.exchange.fetch_ohlcv(
                    symbol=symbol,
                    timeframe=self.timeframe,
                    limit=self.limit
                )
                
                if not ohlcv:
                    logger.warning(f"No data received for {symbol}")
                    continue
                    
                # Convert to DataFrame
                df = pd.DataFrame(
                    ohlcv,
                    columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
                )
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                
                # Store market data
                self.market_data[symbol] = df
                
                # Extract price data (OHLC)
                price_data = df[['open', 'high', 'low', 'close']].values
                all_price_data.append(price_data)
                
                # Create basic features
                features = self._create_features(df)
                all_feature_data.append(features)
                
                logger.info(f"Loaded {len(df)} candles for {symbol}")
                
                # Rate limiting
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {e}")
                continue
                
        # Combine data from all symbols
        if all_price_data:
            price_data = np.concatenate(all_price_data, axis=1)
            feature_data = np.concatenate(all_feature_data, axis=1)
        else:
            price_data = np.array([])
            feature_data = np.array([])
            
        return price_data, feature_data
        
    def _create_features(self, df: pd.DataFrame) -> np.ndarray:
        """
        Create technical features from OHLCV data.
        
        Args:
            df: OHLCV DataFrame
            
        Returns:
            Feature array
        """
        features = []
        
        # Price features
        features.append(df['close'].values.reshape(-1, 1))
        features.append(df['volume'].values.reshape(-1, 1))
        
        # Returns
        returns = df['close'].pct_change().fillna(0).values.reshape(-1, 1)
        features.append(returns)
        
        # Moving averages
        for window in [5, 10, 20]:
            ma = df['close'].rolling(window=window).mean().fillna(method='bfill').values.reshape(-1, 1)
            features.append(ma)
            
        # Volatility
        volatility = df['close'].rolling(window=20).std().fillna(method='bfill').values.reshape(-1, 1)
        features.append(volatility)
        
        # RSI (simplified)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        rsi = rsi.fillna(50).values.reshape(-1, 1)
        features.append(rsi)
        
        # Combine all features
        return np.concatenate(features, axis=1)
        
    def get_data_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded data.
        
        Returns:
            Dictionary containing data information
        """
        info = {
            "exchange": self.exchange_name,
            "symbols": self.symbols,
            "timeframe": self.timeframe,
            "limit": self.limit,
            "market_data_shapes": {}
        }
        
        for symbol, df in self.market_data.items():
            info["market_data_shapes"][symbol] = df.shape
            
        return info
        
    def fetch_latest_data(self, symbol: str, count: int = 100) -> pd.DataFrame:
        """
        Fetch the latest market data for a symbol.
        
        Args:
            symbol: Trading symbol
            count: Number of latest candles to fetch
            
        Returns:
            DataFrame with latest market data
        """
        try:
            ohlcv = self.exchange.fetch_ohlcv(
                symbol=symbol,
                timeframe=self.timeframe,
                limit=count
            )
            
            df = pd.DataFrame(
                ohlcv,
                columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
            )
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error fetching latest data for {symbol}: {e}")
            return pd.DataFrame()
            
    def get_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """
        Fetch historical data for a specific date range.
        
        Args:
            symbol: Trading symbol
            start_date: Start date
            end_date: End date
            
        Returns:
            DataFrame with historical data
        """
        try:
            # Convert dates to timestamps
            since = int(start_date.timestamp() * 1000)
            
            all_data = []
            current_time = since
            end_time = int(end_date.timestamp() * 1000)
            
            while current_time < end_time:
                ohlcv = self.exchange.fetch_ohlcv(
                    symbol=symbol,
                    timeframe=self.timeframe,
                    since=current_time,
                    limit=self.limit
                )
                
                if not ohlcv:
                    break
                    
                all_data.extend(ohlcv)
                current_time = ohlcv[-1][0] + 1
                
                # Rate limiting
                time.sleep(0.1)
                
            if all_data:
                df = pd.DataFrame(
                    all_data,
                    columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
                )
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                df = df.drop_duplicates()
                
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return pd.DataFrame()
