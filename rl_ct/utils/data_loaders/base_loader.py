"""
Base data loader interface.
"""

from abc import ABC, abstractmethod
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
import numpy as np
import pandas as pd


class BaseDataLoader(ABC):
    """Base class for all data loaders."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the data loader.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
    @abstractmethod
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Load and return data.
        
        Returns:
            Tuple of (price_data, feature_data)
        """
        pass
        
    @abstractmethod
    def get_data_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded data.
        
        Returns:
            Dictionary containing data information
        """
        pass
        
    def validate_data(self, data: np.ndarray) -> bool:
        """
        Validate the loaded data.
        
        Args:
            data: Data array to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        if data is None or len(data) == 0:
            return False
            
        # Check for NaN values
        if np.isnan(data).any():
            return False
            
        # Check for infinite values
        if np.isinf(data).any():
            return False
            
        return True
        
    def preprocess_data(self, data: np.ndarray) -> np.ndarray:
        """
        Basic preprocessing of data.
        
        Args:
            data: Raw data array
            
        Returns:
            Preprocessed data array
        """
        # Remove NaN values
        data = np.nan_to_num(data, nan=0.0, posinf=0.0, neginf=0.0)
        
        return data
