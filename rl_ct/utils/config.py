"""
Configuration management utilities.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
from omegaconf import OmegaConf, DictConfig

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


def load_config(config_path: str) -> DictConfig:
    """
    Load configuration from YAML file.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    config_path = Path(config_path)
    
    if not config_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
    try:
        with open(config_path, 'r') as f:
            config_dict = yaml.safe_load(f)
            
        # Convert to OmegaConf for better handling
        config = OmegaConf.create(config_dict)
        
        logger.info(f"Loaded configuration from {config_path}")
        return config
        
    except Exception as e:
        logger.error(f"Error loading configuration from {config_path}: {e}")
        raise


def save_config(config: DictConfig, config_path: str) -> None:
    """
    Save configuration to YAML file.
    
    Args:
        config: Configuration to save
        config_path: Path to save configuration
    """
    config_path = Path(config_path)
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # Convert OmegaConf to dict for YAML serialization
        config_dict = OmegaConf.to_yaml(config)
        
        with open(config_path, 'w') as f:
            f.write(config_dict)
            
        logger.info(f"Saved configuration to {config_path}")
        
    except Exception as e:
        logger.error(f"Error saving configuration to {config_path}: {e}")
        raise


def merge_configs(base_config: DictConfig, override_config: DictConfig) -> DictConfig:
    """
    Merge two configurations with override taking precedence.
    
    Args:
        base_config: Base configuration
        override_config: Override configuration
        
    Returns:
        Merged configuration
    """
    merged = OmegaConf.merge(base_config, override_config)
    logger.debug("Merged configurations")
    return merged


def validate_config(config: DictConfig, required_keys: list) -> bool:
    """
    Validate that configuration contains required keys.
    
    Args:
        config: Configuration to validate
        required_keys: List of required keys
        
    Returns:
        True if valid, False otherwise
    """
    missing_keys = []
    
    for key in required_keys:
        if key not in config:
            missing_keys.append(key)
            
    if missing_keys:
        logger.error(f"Missing required configuration keys: {missing_keys}")
        return False
        
    logger.debug("Configuration validation passed")
    return True


@dataclass
class TrainingConfig:
    """Training configuration dataclass."""
    
    # Environment settings
    env_name: str = "CryptoTradingEnv"
    dataset_name: str = "default"
    episode_length: int = 168
    lookback_window: int = 168
    
    # Training settings
    algorithm: str = "PPO"
    total_timesteps: int = int(1e6)
    learning_rate: float = 3e-4
    batch_size: int = 64
    n_epochs: int = 10
    
    # Model settings
    model_type: str = "transformer"
    hidden_size: int = 256
    num_layers: int = 3
    num_heads: int = 8
    dropout: float = 0.1
    
    # Evaluation settings
    eval_freq: int = 10000
    eval_episodes: int = 10
    
    # Logging settings
    log_interval: int = 1000
    save_freq: int = 50000
    
    # Hardware settings
    device: str = "auto"
    num_workers: int = 4
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TrainingConfig':
        """Create from dictionary."""
        return cls(**config_dict)


@dataclass
class EnvironmentConfig:
    """Environment configuration dataclass."""
    
    # Trading settings
    initial_balance: float = 10000.0
    leverage: float = 1.0
    transaction_cost: float = 0.001
    slippage: float = 0.0001
    
    # Data settings
    symbols: list = None
    timeframe: str = "1h"
    features: list = None
    
    # Risk management
    max_position_size: float = 1.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # Reward settings
    reward_type: str = "pnl"
    reward_scaling: float = 1.0
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["BTC/USDT"]
        if self.features is None:
            self.features = ["close", "volume", "rsi", "macd"]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'EnvironmentConfig':
        """Create from dictionary."""
        return cls(**config_dict)


@dataclass
class ModelConfig:
    """Model configuration dataclass."""
    
    # Architecture
    model_type: str = "transformer"
    input_size: int = 100
    hidden_size: int = 256
    output_size: int = 3  # buy, sell, hold
    
    # Transformer specific
    num_layers: int = 3
    num_heads: int = 8
    dropout: float = 0.1
    activation: str = "gelu"
    
    # Training
    learning_rate: float = 3e-4
    weight_decay: float = 1e-5
    gradient_clip: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ModelConfig':
        """Create from dictionary."""
        return cls(**config_dict)


def get_default_config() -> DictConfig:
    """
    Get default configuration.
    
    Returns:
        Default configuration
    """
    default_config = {
        "training": TrainingConfig().to_dict(),
        "environment": EnvironmentConfig().to_dict(),
        "model": ModelConfig().to_dict(),
    }
    
    return OmegaConf.create(default_config)


def setup_config_from_args(args: Any) -> DictConfig:
    """
    Setup configuration from command line arguments.
    
    Args:
        args: Command line arguments
        
    Returns:
        Configuration
    """
    # Load base configuration
    if hasattr(args, 'config') and args.config:
        config = load_config(args.config)
    else:
        config = get_default_config()
    
    # Override with command line arguments
    overrides = {}
    
    if hasattr(args, 'learning_rate') and args.learning_rate:
        overrides['training.learning_rate'] = args.learning_rate
        
    if hasattr(args, 'batch_size') and args.batch_size:
        overrides['training.batch_size'] = args.batch_size
        
    if hasattr(args, 'total_timesteps') and args.total_timesteps:
        overrides['training.total_timesteps'] = args.total_timesteps
    
    # Apply overrides
    if overrides:
        override_config = OmegaConf.create(overrides)
        config = OmegaConf.merge(config, override_config)
    
    return config
