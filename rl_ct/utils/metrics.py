"""
Trading performance metrics and analysis utilities.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import warnings

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TradeResult:
    """Individual trade result."""
    entry_time: int
    exit_time: int
    entry_price: float
    exit_price: float
    quantity: float
    side: str  # 'long' or 'short'
    pnl: float
    return_pct: float
    duration: int


class TradingMetrics:
    """Calculate and track trading performance metrics."""
    
    def __init__(self):
        """Initialize trading metrics calculator."""
        self.trades: List[TradeResult] = []
        self.equity_curve: List[float] = []
        self.returns: List[float] = []
        self.drawdowns: List[float] = []
        
    def add_trade(
        self,
        entry_time: int,
        exit_time: int,
        entry_price: float,
        exit_price: float,
        quantity: float,
        side: str
    ) -> None:
        """
        Add a completed trade.
        
        Args:
            entry_time: Entry timestamp
            exit_time: Exit timestamp
            entry_price: Entry price
            exit_price: Exit price
            quantity: Trade quantity
            side: Trade side ('long' or 'short')
        """
        if side == 'long':
            pnl = quantity * (exit_price - entry_price)
            return_pct = (exit_price - entry_price) / entry_price
        else:  # short
            pnl = quantity * (entry_price - exit_price)
            return_pct = (entry_price - exit_price) / entry_price
            
        trade = TradeResult(
            entry_time=entry_time,
            exit_time=exit_time,
            entry_price=entry_price,
            exit_price=exit_price,
            quantity=quantity,
            side=side,
            pnl=pnl,
            return_pct=return_pct,
            duration=exit_time - entry_time
        )
        
        self.trades.append(trade)
        
    def update_equity(self, equity: float) -> None:
        """
        Update equity curve.
        
        Args:
            equity: Current equity value
        """
        self.equity_curve.append(equity)
        
        # Calculate return
        if len(self.equity_curve) > 1:
            prev_equity = self.equity_curve[-2]
            if prev_equity > 0:
                return_pct = (equity - prev_equity) / prev_equity
                self.returns.append(return_pct)
            else:
                self.returns.append(0.0)
        
        # Calculate drawdown
        if self.equity_curve:
            peak = max(self.equity_curve)
            drawdown = (peak - equity) / peak if peak > 0 else 0
            self.drawdowns.append(drawdown)
            
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        Calculate comprehensive trading metrics.
        
        Returns:
            Dictionary of trading metrics
        """
        if not self.trades and not self.equity_curve:
            return {}
            
        metrics = {}
        
        # Trade-based metrics
        if self.trades:
            metrics.update(self._calculate_trade_metrics())
            
        # Return-based metrics
        if self.returns:
            metrics.update(self._calculate_return_metrics())
            
        # Equity-based metrics
        if self.equity_curve:
            metrics.update(self._calculate_equity_metrics())
            
        return metrics
        
    def _calculate_trade_metrics(self) -> Dict[str, Any]:
        """Calculate trade-based metrics."""
        if not self.trades:
            return {}
            
        pnls = [trade.pnl for trade in self.trades]
        returns = [trade.return_pct for trade in self.trades]
        durations = [trade.duration for trade in self.trades]
        
        winning_trades = [pnl for pnl in pnls if pnl > 0]
        losing_trades = [pnl for pnl in pnls if pnl < 0]
        
        metrics = {
            'total_trades': len(self.trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': len(winning_trades) / len(self.trades) if self.trades else 0,
            'total_pnl': sum(pnls),
            'avg_pnl_per_trade': np.mean(pnls),
            'avg_return_per_trade': np.mean(returns),
            'avg_winning_trade': np.mean(winning_trades) if winning_trades else 0,
            'avg_losing_trade': np.mean(losing_trades) if losing_trades else 0,
            'largest_winning_trade': max(winning_trades) if winning_trades else 0,
            'largest_losing_trade': min(losing_trades) if losing_trades else 0,
            'avg_trade_duration': np.mean(durations),
            'profit_factor': (
                abs(sum(winning_trades)) / abs(sum(losing_trades))
                if losing_trades and sum(losing_trades) != 0 else float('inf')
            ),
        }
        
        # Consecutive wins/losses
        consecutive_wins, consecutive_losses = self._calculate_consecutive_trades()
        metrics.update({
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses,
        })
        
        return metrics
        
    def _calculate_return_metrics(self) -> Dict[str, Any]:
        """Calculate return-based metrics."""
        if not self.returns:
            return {}
            
        returns_array = np.array(self.returns)
        
        # Basic statistics
        total_return = np.prod(1 + returns_array) - 1
        annualized_return = (1 + total_return) ** (252 / len(returns_array)) - 1
        volatility = np.std(returns_array) * np.sqrt(252)
        
        # Risk-adjusted metrics
        sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
        
        # Downside metrics
        downside_returns = returns_array[returns_array < 0]
        downside_volatility = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = annualized_return / downside_volatility if downside_volatility > 0 else 0
        
        metrics = {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'skewness': self._calculate_skewness(returns_array),
            'kurtosis': self._calculate_kurtosis(returns_array),
        }
        
        return metrics
        
    def _calculate_equity_metrics(self) -> Dict[str, Any]:
        """Calculate equity curve-based metrics."""
        if not self.equity_curve:
            return {}
            
        equity_array = np.array(self.equity_curve)
        
        # Drawdown metrics
        max_drawdown = max(self.drawdowns) if self.drawdowns else 0
        
        # Calmar ratio
        annualized_return = self._get_annualized_return_from_equity()
        calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0
        
        # Recovery metrics
        recovery_factor = abs(annualized_return) / max_drawdown if max_drawdown > 0 else 0
        
        metrics = {
            'max_drawdown': max_drawdown,
            'calmar_ratio': calmar_ratio,
            'recovery_factor': recovery_factor,
            'final_equity': equity_array[-1] if len(equity_array) > 0 else 0,
            'peak_equity': max(equity_array) if len(equity_array) > 0 else 0,
        }
        
        return metrics
        
    def _calculate_consecutive_trades(self) -> Tuple[int, int]:
        """Calculate maximum consecutive wins and losses."""
        if not self.trades:
            return 0, 0
            
        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0
        
        for trade in self.trades:
            if trade.pnl > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            elif trade.pnl < 0:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)
            else:
                current_wins = 0
                current_losses = 0
                
        return max_wins, max_losses
        
    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """Calculate skewness of returns."""
        if len(returns) < 3:
            return 0.0
            
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
            
        skewness = np.mean(((returns - mean_return) / std_return) ** 3)
        return skewness
        
    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """Calculate kurtosis of returns."""
        if len(returns) < 4:
            return 0.0
            
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
            
        kurtosis = np.mean(((returns - mean_return) / std_return) ** 4) - 3
        return kurtosis
        
    def _get_annualized_return_from_equity(self) -> float:
        """Calculate annualized return from equity curve."""
        if len(self.equity_curve) < 2:
            return 0.0
            
        initial_equity = self.equity_curve[0]
        final_equity = self.equity_curve[-1]
        
        if initial_equity <= 0:
            return 0.0
            
        total_return = (final_equity - initial_equity) / initial_equity
        periods = len(self.equity_curve)
        
        # Assume daily data, adjust as needed
        annualized_return = (1 + total_return) ** (252 / periods) - 1
        
        return annualized_return
        
    def get_trade_summary(self) -> pd.DataFrame:
        """
        Get summary of all trades as DataFrame.
        
        Returns:
            DataFrame with trade details
        """
        if not self.trades:
            return pd.DataFrame()
            
        trade_data = []
        for i, trade in enumerate(self.trades):
            trade_data.append({
                'trade_id': i + 1,
                'entry_time': trade.entry_time,
                'exit_time': trade.exit_time,
                'entry_price': trade.entry_price,
                'exit_price': trade.exit_price,
                'quantity': trade.quantity,
                'side': trade.side,
                'pnl': trade.pnl,
                'return_pct': trade.return_pct,
                'duration': trade.duration,
            })
            
        return pd.DataFrame(trade_data)
        
    def reset(self) -> None:
        """Reset all metrics and data."""
        self.trades.clear()
        self.equity_curve.clear()
        self.returns.clear()
        self.drawdowns.clear()
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert metrics to dictionary.
        
        Returns:
            Dictionary representation of metrics
        """
        return {
            'metrics': self.calculate_metrics(),
            'num_trades': len(self.trades),
            'num_equity_points': len(self.equity_curve),
        }
