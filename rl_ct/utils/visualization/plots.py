"""
Plotting utilities for trading analysis and model visualization.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class TradingPlotter:
    """Trading-specific plotting utilities."""
    
    def __init__(self, style: str = "seaborn-v0_8", figsize: Tuple[int, int] = (12, 8)):
        """
        Initialize trading plotter.
        
        Args:
            style: Matplotlib style
            figsize: Default figure size
        """
        self.style = style
        self.figsize = figsize
        
        # Set style
        plt.style.use(style)
        sns.set_palette("husl")
        
    def plot_price_action(
        self,
        data: pd.DataFrame,
        title: str = "Price Action",
        save_path: Optional[str] = None,
        show_volume: bool = True
    ) -> None:
        """
        Plot OHLCV price action.
        
        Args:
            data: DataFrame with OHLCV data
            title: Plot title
            save_path: Path to save plot
            show_volume: Whether to show volume subplot
        """
        fig, axes = plt.subplots(2 if show_volume else 1, 1, 
                                figsize=(self.figsize[0], self.figsize[1] * (1.5 if show_volume else 1)),
                                gridspec_kw={'height_ratios': [3, 1]} if show_volume else None)
        
        if not show_volume:
            axes = [axes]
            
        # Price plot
        if 'close' in data.columns:
            axes[0].plot(data.index, data['close'], label='Close', linewidth=2)
            
        if all(col in data.columns for col in ['high', 'low']):
            axes[0].fill_between(data.index, data['low'], data['high'], 
                                alpha=0.3, label='High-Low Range')
                                
        axes[0].set_title(title)
        axes[0].set_ylabel('Price')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Volume plot
        if show_volume and 'volume' in data.columns:
            axes[1].bar(data.index, data['volume'], alpha=0.7, color='gray')
            axes[1].set_ylabel('Volume')
            axes[1].set_xlabel('Time')
            axes[1].grid(True, alpha=0.3)
            
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Price action plot saved to {save_path}")
            
        plt.show()
        
    def plot_technical_indicators(
        self,
        data: pd.DataFrame,
        indicators: List[str],
        title: str = "Technical Indicators",
        save_path: Optional[str] = None
    ) -> None:
        """
        Plot technical indicators.
        
        Args:
            data: DataFrame with indicator data
            indicators: List of indicator column names
            title: Plot title
            save_path: Path to save plot
        """
        n_indicators = len(indicators)
        fig, axes = plt.subplots(n_indicators, 1, figsize=(self.figsize[0], self.figsize[1] * n_indicators / 2))
        
        if n_indicators == 1:
            axes = [axes]
            
        for i, indicator in enumerate(indicators):
            if indicator in data.columns:
                axes[i].plot(data.index, data[indicator], label=indicator, linewidth=2)
                axes[i].set_title(f"{indicator}")
                axes[i].set_ylabel(indicator)
                axes[i].legend()
                axes[i].grid(True, alpha=0.3)
            else:
                logger.warning(f"Indicator {indicator} not found in data")
                
        axes[-1].set_xlabel('Time')
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Technical indicators plot saved to {save_path}")
            
        plt.show()
        
    def plot_equity_curve(
        self,
        equity_data: List[float],
        benchmark_data: Optional[List[float]] = None,
        title: str = "Equity Curve",
        save_path: Optional[str] = None
    ) -> None:
        """
        Plot equity curve with optional benchmark.
        
        Args:
            equity_data: Portfolio equity values
            benchmark_data: Benchmark equity values
            title: Plot title
            save_path: Path to save plot
        """
        plt.figure(figsize=self.figsize)
        
        # Plot equity curve
        plt.plot(equity_data, label='Portfolio', linewidth=2)
        
        # Plot benchmark if provided
        if benchmark_data:
            plt.plot(benchmark_data, label='Benchmark', linewidth=2, alpha=0.7)
            
        plt.title(title)
        plt.xlabel('Time')
        plt.ylabel('Portfolio Value')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Add performance metrics as text
        if len(equity_data) > 1:
            total_return = (equity_data[-1] - equity_data[0]) / equity_data[0]
            plt.text(0.02, 0.98, f'Total Return: {total_return:.2%}', 
                    transform=plt.gca().transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                    
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Equity curve plot saved to {save_path}")
            
        plt.show()
        
    def plot_drawdown(
        self,
        equity_data: List[float],
        title: str = "Drawdown Analysis",
        save_path: Optional[str] = None
    ) -> None:
        """
        Plot drawdown analysis.
        
        Args:
            equity_data: Portfolio equity values
            title: Plot title
            save_path: Path to save plot
        """
        # Calculate drawdown
        equity_array = np.array(equity_data)
        peak = np.maximum.accumulate(equity_array)
        drawdown = (peak - equity_array) / peak
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.figsize[0], self.figsize[1] * 1.2))
        
        # Equity curve with peaks
        ax1.plot(equity_array, label='Equity', linewidth=2)
        ax1.plot(peak, label='Peak', linewidth=1, alpha=0.7, linestyle='--')
        ax1.set_title('Equity Curve with Peaks')
        ax1.set_ylabel('Portfolio Value')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Drawdown
        ax2.fill_between(range(len(drawdown)), drawdown, alpha=0.7, color='red')
        ax2.set_title('Drawdown')
        ax2.set_xlabel('Time')
        ax2.set_ylabel('Drawdown')
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        ax2.grid(True, alpha=0.3)
        
        # Add max drawdown text
        max_dd = np.max(drawdown)
        ax2.text(0.02, 0.98, f'Max Drawdown: {max_dd:.2%}', 
                transform=ax2.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Drawdown plot saved to {save_path}")
            
        plt.show()
        
    def plot_returns_analysis(
        self,
        returns: List[float],
        title: str = "Returns Analysis",
        save_path: Optional[str] = None
    ) -> None:
        """
        Plot returns distribution and statistics.
        
        Args:
            returns: List of returns
            title: Plot title
            save_path: Path to save plot
        """
        returns_array = np.array(returns)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(self.figsize[0] * 1.5, self.figsize[1] * 1.2))
        
        # Returns time series
        ax1.plot(returns_array, alpha=0.8)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax1.set_title('Returns Time Series')
        ax1.set_ylabel('Returns')
        ax1.grid(True, alpha=0.3)
        
        # Returns histogram
        ax2.hist(returns_array, bins=50, alpha=0.7, edgecolor='black')
        ax2.axvline(np.mean(returns_array), color='red', linestyle='--', 
                   label=f'Mean: {np.mean(returns_array):.4f}')
        ax2.set_title('Returns Distribution')
        ax2.set_xlabel('Returns')
        ax2.set_ylabel('Frequency')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Cumulative returns
        cumulative_returns = np.cumprod(1 + returns_array) - 1
        ax3.plot(cumulative_returns)
        ax3.set_title('Cumulative Returns')
        ax3.set_ylabel('Cumulative Returns')
        ax3.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        ax3.grid(True, alpha=0.3)
        
        # Rolling volatility
        window = min(30, len(returns_array) // 4)
        if window > 5:
            rolling_vol = pd.Series(returns_array).rolling(window).std()
            ax4.plot(rolling_vol)
            ax4.set_title(f'Rolling Volatility (Window: {window})')
            ax4.set_xlabel('Time')
            ax4.set_ylabel('Volatility')
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, 'Insufficient data\nfor rolling volatility', 
                    ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('Rolling Volatility')
            
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Returns analysis plot saved to {save_path}")
            
        plt.show()


class ModelAnalyzer:
    """Model analysis and visualization utilities."""
    
    def __init__(self):
        """Initialize model analyzer."""
        pass
        
    def plot_training_curves(
        self,
        training_data: Dict[str, List[float]],
        title: str = "Training Curves",
        save_path: Optional[str] = None
    ) -> None:
        """
        Plot training curves (loss, reward, etc.).
        
        Args:
            training_data: Dictionary with training metrics
            title: Plot title
            save_path: Path to save plot
        """
        n_metrics = len(training_data)
        fig, axes = plt.subplots(n_metrics, 1, figsize=(12, 4 * n_metrics))
        
        if n_metrics == 1:
            axes = [axes]
            
        for i, (metric_name, values) in enumerate(training_data.items()):
            axes[i].plot(values, linewidth=2)
            axes[i].set_title(f"{metric_name}")
            axes[i].set_ylabel(metric_name)
            axes[i].grid(True, alpha=0.3)
            
        axes[-1].set_xlabel('Training Steps')
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Training curves plot saved to {save_path}")
            
        plt.show()
        
    def plot_action_distribution(
        self,
        actions: List[int],
        action_names: Optional[Dict[int, str]] = None,
        title: str = "Action Distribution",
        save_path: Optional[str] = None
    ) -> None:
        """
        Plot action distribution.
        
        Args:
            actions: List of actions taken
            action_names: Mapping of action indices to names
            title: Plot title
            save_path: Path to save plot
        """
        if action_names is None:
            action_names = {0: 'Hold', 1: 'Buy', 2: 'Sell', 3: 'Close'}
            
        # Count actions
        action_counts = {}
        for action in actions:
            name = action_names.get(action, f'Action_{action}')
            action_counts[name] = action_counts.get(name, 0) + 1
            
        # Create plot
        plt.figure(figsize=(10, 6))
        names = list(action_counts.keys())
        counts = list(action_counts.values())
        
        bars = plt.bar(names, counts, alpha=0.8)
        
        # Add percentage labels
        total = sum(counts)
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{count}\n({count/total:.1%})', ha='center', va='bottom')
                    
        plt.title(title)
        plt.xlabel('Action')
        plt.ylabel('Frequency')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Action distribution plot saved to {save_path}")
            
        plt.show()
        
    def create_interactive_equity_plot(
        self,
        equity_data: List[float],
        timestamps: Optional[List] = None,
        title: str = "Interactive Equity Curve"
    ) -> go.Figure:
        """
        Create interactive equity curve using Plotly.
        
        Args:
            equity_data: Portfolio equity values
            timestamps: Optional timestamps
            title: Plot title
            
        Returns:
            Plotly figure
        """
        if timestamps is None:
            timestamps = list(range(len(equity_data)))
            
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=equity_data,
            mode='lines',
            name='Portfolio Value',
            line=dict(width=2)
        ))
        
        fig.update_layout(
            title=title,
            xaxis_title='Time',
            yaxis_title='Portfolio Value',
            hovermode='x unified',
            showlegend=True
        )
        
        return fig
