"""
Tests for neural network models.
"""

import pytest
import torch
import torch.nn as nn
import numpy as np

from rl_ct.models.base_model import BaseModel, ModelConfig
from rl_ct.models.transformer import TransformerModel, TransformerConfig
from rl_ct.models.mlp import MLPModel, MLPConfig


class TestModelConfig:
    """Test model configuration."""
    
    def test_default_config(self):
        """Test default configuration."""
        config = ModelConfig()
        
        assert config.input_size == 100
        assert config.hidden_size == 256
        assert config.output_size == 3
        assert config.num_layers == 3
        assert config.dropout == 0.1
        assert config.activation == "relu"
        assert config.learning_rate == 3e-4
        
    def test_device_auto_selection(self):
        """Test automatic device selection."""
        config = ModelConfig(device="auto")
        
        expected_device = "cuda" if torch.cuda.is_available() else "cpu"
        assert config.device == expected_device


class TestTransformerModel:
    """Test Transformer model."""
    
    @pytest.fixture
    def transformer_config(self):
        """Transformer configuration for testing."""
        return TransformerConfig(
            input_size=1680,  # 168 * 10
            output_size=4,
            d_model=64,
            num_heads=4,
            d_ff=128,
            num_encoder_layers=2,
            lookback_window=168,
            feature_dim=10,
            max_seq_length=200,
            dropout=0.1
        )
        
    def test_initialization(self, transformer_config):
        """Test model initialization."""
        model = TransformerModel(transformer_config)
        
        assert isinstance(model, BaseModel)
        assert isinstance(model, nn.Module)
        assert model.config == transformer_config
        
        # Check if model has required components
        assert hasattr(model, 'encoder')
        assert hasattr(model, 'policy_head')
        assert hasattr(model, 'value_head')
        
    def test_forward_pass(self, transformer_config):
        """Test forward pass."""
        model = TransformerModel(transformer_config)
        
        batch_size = 4
        input_tensor = torch.randn(batch_size, transformer_config.input_size)
        
        policy_logits, value = model(input_tensor)
        
        assert policy_logits.shape == (batch_size, transformer_config.output_size)
        assert value.shape == (batch_size,)
        
    def test_predict(self, transformer_config):
        """Test prediction method."""
        model = TransformerModel(transformer_config)
        
        batch_size = 2
        input_tensor = torch.randn(batch_size, transformer_config.input_size)
        
        # Test deterministic prediction
        action_det = model.predict(input_tensor, deterministic=True)
        assert action_det.shape == (batch_size,)
        assert action_det.dtype == torch.long
        
        # Test stochastic prediction
        action_stoch = model.predict(input_tensor, deterministic=False)
        assert action_stoch.shape == (batch_size,)
        assert action_stoch.dtype == torch.long
        
    def test_evaluate_actions(self, transformer_config):
        """Test action evaluation."""
        model = TransformerModel(transformer_config)
        
        batch_size = 3
        input_tensor = torch.randn(batch_size, transformer_config.input_size)
        actions = torch.randint(0, transformer_config.output_size, (batch_size,))
        
        log_probs, values, entropy = model.evaluate_actions(input_tensor, actions)
        
        assert log_probs.shape == (batch_size,)
        assert values.shape == (batch_size,)
        assert entropy.shape == (batch_size,)
        
    def test_parameter_count(self, transformer_config):
        """Test parameter counting."""
        model = TransformerModel(transformer_config)
        
        total_params = model.get_num_parameters()
        trainable_params = model.get_num_trainable_parameters()
        
        assert total_params > 0
        assert trainable_params > 0
        assert trainable_params <= total_params
        
    def test_save_load(self, transformer_config, tmp_path):
        """Test model saving and loading."""
        model = TransformerModel(transformer_config)
        
        # Save model
        save_path = tmp_path / "test_model.pt"
        model.save(str(save_path))
        
        assert save_path.exists()
        
        # Load model
        loaded_model = TransformerModel.load(str(save_path))
        
        assert isinstance(loaded_model, TransformerModel)
        assert loaded_model.config.input_size == transformer_config.input_size
        assert loaded_model.config.output_size == transformer_config.output_size


class TestMLPModel:
    """Test MLP model."""
    
    @pytest.fixture
    def mlp_config(self):
        """MLP configuration for testing."""
        return MLPConfig(
            input_size=100,
            output_size=4,
            hidden_size=64,
            hidden_layers=[64, 32],
            num_layers=2,
            dropout=0.1,
            use_layer_norm=True
        )
        
    def test_initialization(self, mlp_config):
        """Test model initialization."""
        model = MLPModel(mlp_config)
        
        assert isinstance(model, BaseModel)
        assert isinstance(model, nn.Module)
        assert model.config == mlp_config
        
        # Check if model has required components
        assert hasattr(model, 'encoder')
        assert hasattr(model, 'policy_head')
        assert hasattr(model, 'value_head')
        
    def test_forward_pass(self, mlp_config):
        """Test forward pass."""
        model = MLPModel(mlp_config)
        
        batch_size = 4
        input_tensor = torch.randn(batch_size, mlp_config.input_size)
        
        policy_logits, value = model(input_tensor)
        
        assert policy_logits.shape == (batch_size, mlp_config.output_size)
        assert value.shape == (batch_size,)
        
    def test_different_hidden_layers(self):
        """Test with different hidden layer configurations."""
        configs = [
            [128],  # Single layer
            [128, 64],  # Two layers
            [256, 128, 64],  # Three layers
        ]
        
        for hidden_layers in configs:
            config = MLPConfig(
                input_size=100,
                output_size=4,
                hidden_layers=hidden_layers
            )
            
            model = MLPModel(config)
            
            batch_size = 2
            input_tensor = torch.randn(batch_size, config.input_size)
            
            policy_logits, value = model(input_tensor)
            
            assert policy_logits.shape == (batch_size, config.output_size)
            assert value.shape == (batch_size,)
            
    def test_normalization_options(self):
        """Test different normalization options."""
        # Test with batch norm
        config_bn = MLPConfig(
            input_size=50,
            output_size=3,
            use_batch_norm=True,
            use_layer_norm=False
        )
        model_bn = MLPModel(config_bn)
        
        # Test with layer norm
        config_ln = MLPConfig(
            input_size=50,
            output_size=3,
            use_batch_norm=False,
            use_layer_norm=True
        )
        model_ln = MLPModel(config_ln)
        
        # Test with no norm
        config_no = MLPConfig(
            input_size=50,
            output_size=3,
            use_batch_norm=False,
            use_layer_norm=False
        )
        model_no = MLPModel(config_no)
        
        # All should work
        input_tensor = torch.randn(2, 50)
        
        for model in [model_bn, model_ln, model_no]:
            policy_logits, value = model(input_tensor)
            assert policy_logits.shape == (2, 3)
            assert value.shape == (2,)


class TestBaseModel:
    """Test base model functionality."""
    
    def test_abstract_methods(self):
        """Test that BaseModel is abstract."""
        config = ModelConfig()
        
        # Should not be able to instantiate BaseModel directly
        with pytest.raises(TypeError):
            BaseModel(config)
            
    def test_model_info(self):
        """Test model info method."""
        config = TransformerConfig(
            input_size=100,
            output_size=4,
            d_model=64,
            lookback_window=10,
            feature_dim=10
        )
        model = TransformerModel(config)
        
        info = model.get_model_info()
        
        assert isinstance(info, dict)
        assert 'model_type' in info
        assert 'config' in info
        assert 'device' in info
        assert 'num_parameters' in info
        assert 'num_trainable_parameters' in info
        
    def test_device_management(self):
        """Test device management."""
        config = TransformerConfig(
            input_size=100,
            output_size=4,
            d_model=64,
            lookback_window=10,
            feature_dim=10,
            device='cpu'
        )
        model = TransformerModel(config)
        
        # Should be on CPU
        assert model.device.type == 'cpu'
        
        # Move to CPU explicitly (should work even if already on CPU)
        model.to_device('cpu')
        assert model.device.type == 'cpu'
        
    def test_freeze_unfreeze(self):
        """Test parameter freezing."""
        config = TransformerConfig(
            input_size=100,
            output_size=4,
            d_model=64,
            lookback_window=10,
            feature_dim=10
        )
        model = TransformerModel(config)
        
        # Initially all parameters should be trainable
        initial_trainable = model.get_num_trainable_parameters()
        assert initial_trainable > 0
        
        # Freeze encoder
        model.freeze_encoder()
        frozen_trainable = model.get_num_trainable_parameters()
        assert frozen_trainable < initial_trainable
        
        # Unfreeze encoder
        model.unfreeze_encoder()
        unfrozen_trainable = model.get_num_trainable_parameters()
        assert unfrozen_trainable == initial_trainable


if __name__ == "__main__":
    pytest.main([__file__])
