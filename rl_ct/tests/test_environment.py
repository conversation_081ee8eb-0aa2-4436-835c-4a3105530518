"""
Tests for trading environments.
"""

import pytest
import numpy as np
import gymnasium as gym
from unittest.mock import Mock, patch

from rl_ct.envs import CryptoTradingEnv, BaseTradingEnv
from rl_ct.utils.config import EnvironmentConfig


class TestBaseTradingEnv:
    """Test base trading environment."""
    
    def test_initialization(self):
        """Test environment initialization."""
        config = {
            'initial_balance': 10000.0,
            'transaction_cost': 0.001,
            'max_steps': 1000
        }
        
        # Create a concrete implementation for testing
        class TestEnv(BaseTradingEnv):
            def _get_observation(self):
                return np.array([1.0, 2.0, 3.0])
                
            def _calculate_reward(self, action, prev_balance):
                return 0.1
                
            def _execute_action(self, action):
                return {'executed': True}
                
        env = TestEnv(config)
        
        assert env.initial_balance == 10000.0
        assert env.transaction_cost == 0.001
        assert env.max_steps == 1000
        assert env.balance == 10000.0
        assert env.position == 0.0
        
    def test_reset(self):
        """Test environment reset."""
        class TestEnv(BaseTradingEnv):
            def _get_observation(self):
                return np.array([1.0, 2.0, 3.0])
                
            def _calculate_reward(self, action, prev_balance):
                return 0.1
                
            def _execute_action(self, action):
                return {'executed': True}
                
        env = TestEnv()
        
        # Modify state
        env.balance = 5000.0
        env.position = 100.0
        env.current_step = 50
        
        # Reset
        obs, info = env.reset()
        
        assert env.balance == env.initial_balance
        assert env.position == 0.0
        assert env.current_step == 0
        assert isinstance(obs, np.ndarray)
        assert isinstance(info, dict)


class TestCryptoTradingEnv:
    """Test cryptocurrency trading environment."""
    
    @pytest.fixture
    def env_config(self):
        """Environment configuration for testing."""
        return {
            'initial_balance': 10000.0,
            'leverage': 1.0,
            'transaction_cost': 0.001,
            'order_size': 100.0,
            'dataset_name': 'test_dataset',
            'lookback_window': 10,
            'episode_length': 100,
            'train_start': [0],
            'train_end': [1000],
            'test_start': [1000],
            'test_end': [2000],
            'regime': 'training',
            'data_dir': 'test_data',
            'scaler': {
                'type': 'quantile',
                'min_quantile': 0.5,
                'max_quantile': 99.5,
                'scale_coef': 10000.0
            }
        }
        
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_initialization(self, mock_loader, env_config):
        """Test environment initialization."""
        # Mock data loader
        mock_loader_instance = Mock()
        mock_loader_instance.load_data.return_value = (
            np.random.randn(2000, 4),  # price data
            np.random.randn(2000, 10)  # feature data
        )
        mock_loader.return_value = mock_loader_instance
        
        env = CryptoTradingEnv(env_config)
        
        assert env.initial_balance == 10000.0
        assert env.leverage == 1.0
        assert env.lookback_window == 10
        assert env.episode_length == 100
        assert env.price_data is not None
        assert env.feature_data is not None
        
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_reset(self, mock_loader, env_config):
        """Test environment reset."""
        # Mock data loader
        mock_loader_instance = Mock()
        mock_loader_instance.load_data.return_value = (
            np.random.randn(2000, 4),  # price data
            np.random.randn(2000, 10)  # feature data
        )
        mock_loader.return_value = mock_loader_instance
        
        env = CryptoTradingEnv(env_config)
        obs, info = env.reset()
        
        assert isinstance(obs, np.ndarray)
        assert obs.shape == env.observation_space.shape
        assert isinstance(info, dict)
        assert env.current_step == 0
        assert env.balance == env.initial_balance
        
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_step(self, mock_loader, env_config):
        """Test environment step."""
        # Mock data loader
        mock_loader_instance = Mock()
        price_data = np.random.randn(2000, 4)
        price_data[:, -1] = np.abs(price_data[:, -1]) + 100  # Ensure positive prices
        mock_loader_instance.load_data.return_value = (
            price_data,
            np.random.randn(2000, 10)  # feature data
        )
        mock_loader.return_value = mock_loader_instance
        
        env = CryptoTradingEnv(env_config)
        obs, info = env.reset()
        
        # Test different actions
        for action in [0, 1, 2, 3]:  # hold, buy, sell, close
            obs, reward, terminated, truncated, info = env.step(action)
            
            assert isinstance(obs, np.ndarray)
            assert isinstance(reward, (int, float))
            assert isinstance(terminated, bool)
            assert isinstance(truncated, bool)
            assert isinstance(info, dict)
            
            if terminated or truncated:
                break
                
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_action_space(self, mock_loader, env_config):
        """Test action space."""
        # Mock data loader
        mock_loader_instance = Mock()
        mock_loader_instance.load_data.return_value = (
            np.random.randn(2000, 4),
            np.random.randn(2000, 10)
        )
        mock_loader.return_value = mock_loader_instance
        
        env = CryptoTradingEnv(env_config)
        
        assert isinstance(env.action_space, gym.spaces.Discrete)
        assert env.action_space.n == 4  # hold, buy, sell, close
        
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_observation_space(self, mock_loader, env_config):
        """Test observation space."""
        # Mock data loader
        mock_loader_instance = Mock()
        feature_data = np.random.randn(2000, 10)
        mock_loader_instance.load_data.return_value = (
            np.random.randn(2000, 4),
            feature_data
        )
        mock_loader.return_value = mock_loader_instance
        
        env = CryptoTradingEnv(env_config)
        
        assert isinstance(env.observation_space, gym.spaces.Box)
        expected_dim = (feature_data.shape[1] + 2) * env_config['lookback_window']
        assert env.observation_space.shape == (expected_dim,)
        
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_position_management(self, mock_loader, env_config):
        """Test position management."""
        # Mock data loader
        mock_loader_instance = Mock()
        price_data = np.ones((2000, 4)) * 100  # Fixed price for predictable testing
        mock_loader_instance.load_data.return_value = (
            price_data,
            np.random.randn(2000, 10)
        )
        mock_loader.return_value = mock_loader_instance
        
        env = CryptoTradingEnv(env_config)
        obs, info = env.reset()
        
        initial_balance = env.balance
        
        # Test buy action
        obs, reward, terminated, truncated, info = env.step(1)  # buy
        
        # Should have opened a long position
        assert env.position_long > 0
        assert env.balance < initial_balance  # Balance reduced by cost
        
        # Test sell action (should close long position)
        obs, reward, terminated, truncated, info = env.step(2)  # sell
        
        # Position should be closed
        assert env.position_long == 0
        
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_reward_calculation(self, mock_loader, env_config):
        """Test reward calculation."""
        # Mock data loader
        mock_loader_instance = Mock()
        price_data = np.ones((2000, 4)) * 100
        mock_loader_instance.load_data.return_value = (
            price_data,
            np.random.randn(2000, 10)
        )
        mock_loader.return_value = mock_loader_instance
        
        env = CryptoTradingEnv(env_config)
        obs, info = env.reset()
        
        # Test that rewards are calculated
        obs, reward, terminated, truncated, info = env.step(0)  # hold
        
        assert isinstance(reward, (int, float))
        # Reward should be finite
        assert np.isfinite(reward)


class TestEnvironmentConfig:
    """Test environment configuration."""
    
    def test_default_config(self):
        """Test default configuration."""
        config = EnvironmentConfig()
        
        assert config.initial_balance == 10000.0
        assert config.leverage == 1.0
        assert config.transaction_cost == 0.001
        assert config.symbols == ["BTC/USDT"]
        assert config.features == ["close", "volume", "rsi", "macd"]
        
    def test_config_from_dict(self):
        """Test configuration from dictionary."""
        config_dict = {
            'initial_balance': 5000.0,
            'leverage': 2.0,
            'symbols': ['ETH/USDT', 'BTC/USDT'],
            'transaction_cost': 0.002
        }
        
        config = EnvironmentConfig.from_dict(config_dict)
        
        assert config.initial_balance == 5000.0
        assert config.leverage == 2.0
        assert config.symbols == ['ETH/USDT', 'BTC/USDT']
        assert config.transaction_cost == 0.002
        
    def test_config_to_dict(self):
        """Test configuration to dictionary."""
        config = EnvironmentConfig(
            initial_balance=15000.0,
            leverage=3.0
        )
        
        config_dict = config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert config_dict['initial_balance'] == 15000.0
        assert config_dict['leverage'] == 3.0


if __name__ == "__main__":
    pytest.main([__file__])
