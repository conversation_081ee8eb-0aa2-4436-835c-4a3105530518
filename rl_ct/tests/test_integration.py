"""
Integration tests for RL-CT framework.
"""

import pytest
import numpy as np
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from rl_ct.envs import CryptoTradingEnv
from rl_ct.models.transformer import TransformerModel, TransformerConfig
from rl_ct.utils.preprocessing import DataProcessor
from rl_ct.utils.data_loaders import DiskDataLoader
from rl_ct.utils.metrics import TradingMetrics


class TestDataPipeline:
    """Test data processing pipeline."""
    
    def test_data_processor_pipeline(self):
        """Test complete data processing pipeline."""
        # Create sample data
        sample_data = {
            'timestamp': pd.date_range('2023-01-01', periods=1000, freq='1H'),
            'open': np.random.randn(1000) * 0.01 + 100,
            'high': np.random.randn(1000) * 0.01 + 101,
            'low': np.random.randn(1000) * 0.01 + 99,
            'close': np.random.randn(1000) * 0.01 + 100,
            'volume': np.random.randn(1000) * 1000 + 10000,
        }
        
        import pandas as pd
        df = pd.DataFrame(sample_data)
        df.set_index('timestamp', inplace=True)
        
        # Create data processor
        config = {
            'scaler': {
                'type': 'quantile',
                'min_quantile': 0.5,
                'max_quantile': 99.5,
                'scale_coef': 10000.0
            }
        }
        processor = DataProcessor(config)
        
        # Process data
        processed_df = processor.create_features(df)
        scaled_data = processor.scale_data(processed_df)
        
        assert processed_df.shape[0] == df.shape[0]
        assert processed_df.shape[1] > df.shape[1]  # Should have more features
        assert scaled_data.shape[0] == df.shape[0]
        assert not np.isnan(scaled_data).any()  # No NaN values
        
    def test_disk_loader_save_load(self):
        """Test saving and loading data with DiskDataLoader."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create sample data
            price_data = np.random.randn(100, 4)
            feature_data = np.random.randn(100, 10)
            
            # Save data
            loader = DiskDataLoader(
                data_dir=temp_dir,
                dataset_name="test_dataset",
                file_format="npy"
            )
            
            metadata = {'test': 'metadata'}
            loader.save_data(price_data, feature_data, metadata)
            
            # Load data
            loaded_price, loaded_features = loader.load_data()
            
            np.testing.assert_array_equal(price_data, loaded_price)
            np.testing.assert_array_equal(feature_data, loaded_features)


class TestModelEnvironmentIntegration:
    """Test model and environment integration."""
    
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_model_environment_interaction(self, mock_loader):
        """Test model interacting with environment."""
        # Mock data loader
        mock_loader_instance = Mock()
        price_data = np.random.randn(1000, 4)
        price_data[:, -1] = np.abs(price_data[:, -1]) + 100  # Positive prices
        feature_data = np.random.randn(1000, 20)
        
        mock_loader_instance.load_data.return_value = (price_data, feature_data)
        mock_loader.return_value = mock_loader_instance
        
        # Create environment
        env_config = {
            'initial_balance': 10000.0,
            'lookback_window': 10,
            'episode_length': 50,
            'dataset_name': 'test',
            'train_start': [0],
            'train_end': [800],
            'test_start': [800],
            'test_end': [1000],
            'regime': 'training',
            'scaler': {
                'type': 'quantile',
                'min_quantile': 0.5,
                'max_quantile': 99.5,
                'scale_coef': 10000.0
            }
        }
        
        env = CryptoTradingEnv(env_config)
        
        # Create model
        model_config = TransformerConfig(
            input_size=env.observation_space.shape[0],
            output_size=env.action_space.n,
            d_model=64,
            num_heads=4,
            d_ff=128,
            num_encoder_layers=2,
            lookback_window=10,
            feature_dim=22,  # 20 features + 2 account info
            dropout=0.1
        )
        
        model = TransformerModel(model_config)
        
        # Test interaction
        obs, info = env.reset()
        
        for _ in range(10):
            # Model prediction
            action = model.predict(torch.tensor(obs).unsqueeze(0), deterministic=True)
            action_int = action.item()
            
            # Environment step
            obs, reward, terminated, truncated, info = env.step(action_int)
            
            assert isinstance(reward, (int, float))
            assert isinstance(terminated, bool)
            assert isinstance(truncated, bool)
            
            if terminated or truncated:
                break
                
        env.close()


class TestTradingMetrics:
    """Test trading metrics calculation."""
    
    def test_metrics_calculation(self):
        """Test comprehensive metrics calculation."""
        metrics = TradingMetrics()
        
        # Add some sample trades
        trades_data = [
            (0, 10, 100.0, 105.0, 1.0, 'long'),   # Winning trade
            (10, 20, 105.0, 103.0, 1.0, 'long'),  # Losing trade
            (20, 30, 103.0, 108.0, 1.0, 'long'),  # Winning trade
            (30, 40, 108.0, 106.0, 1.0, 'short'), # Winning short
        ]
        
        for entry_time, exit_time, entry_price, exit_price, quantity, side in trades_data:
            metrics.add_trade(entry_time, exit_time, entry_price, exit_price, quantity, side)
            
        # Add equity curve
        equity_values = [10000, 10050, 10030, 10080, 10100]
        for equity in equity_values:
            metrics.update_equity(equity)
            
        # Calculate metrics
        calculated_metrics = metrics.calculate_metrics()
        
        assert 'total_trades' in calculated_metrics
        assert 'win_rate' in calculated_metrics
        assert 'total_pnl' in calculated_metrics
        assert 'sharpe_ratio' in calculated_metrics
        assert 'max_drawdown' in calculated_metrics
        
        assert calculated_metrics['total_trades'] == 4
        assert 0 <= calculated_metrics['win_rate'] <= 1
        
    def test_trade_summary_dataframe(self):
        """Test trade summary DataFrame generation."""
        metrics = TradingMetrics()
        
        # Add a trade
        metrics.add_trade(0, 10, 100.0, 105.0, 1.0, 'long')
        
        # Get summary
        summary_df = metrics.get_trade_summary()
        
        assert len(summary_df) == 1
        assert 'trade_id' in summary_df.columns
        assert 'pnl' in summary_df.columns
        assert 'return_pct' in summary_df.columns


class TestConfigurationSystem:
    """Test configuration management."""
    
    def test_config_loading_saving(self):
        """Test configuration loading and saving."""
        from rl_ct.utils.config import load_config, save_config
        from omegaconf import OmegaConf
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test config
            test_config = OmegaConf.create({
                'training': {
                    'total_timesteps': 100000,
                    'learning_rate': 3e-4
                },
                'environment': {
                    'initial_balance': 10000.0,
                    'transaction_cost': 0.001
                }
            })
            
            # Save config
            config_path = Path(temp_dir) / "test_config.yaml"
            save_config(test_config, str(config_path))
            
            assert config_path.exists()
            
            # Load config
            loaded_config = load_config(str(config_path))
            
            assert loaded_config.training.total_timesteps == 100000
            assert loaded_config.training.learning_rate == 3e-4
            assert loaded_config.environment.initial_balance == 10000.0


class TestEndToEndWorkflow:
    """Test end-to-end workflow simulation."""
    
    @patch('rl_ct.envs.crypto_trading_env.DiskDataLoader')
    def test_minimal_training_workflow(self, mock_loader):
        """Test a minimal training workflow."""
        # Mock data loader
        mock_loader_instance = Mock()
        price_data = np.random.randn(500, 4)
        price_data[:, -1] = np.abs(price_data[:, -1]) + 100
        feature_data = np.random.randn(500, 15)
        
        mock_loader_instance.load_data.return_value = (price_data, feature_data)
        mock_loader.return_value = mock_loader_instance
        
        # Environment config
        env_config = {
            'initial_balance': 10000.0,
            'lookback_window': 5,
            'episode_length': 20,
            'dataset_name': 'test',
            'train_start': [0],
            'train_end': [400],
            'test_start': [400],
            'test_end': [500],
            'regime': 'training',
            'scaler': {
                'type': 'quantile',
                'min_quantile': 0.5,
                'max_quantile': 99.5,
                'scale_coef': 10000.0
            }
        }
        
        # Create environment
        env = CryptoTradingEnv(env_config)
        
        # Create model
        model_config = TransformerConfig(
            input_size=env.observation_space.shape[0],
            output_size=env.action_space.n,
            d_model=32,
            num_heads=2,
            d_ff=64,
            num_encoder_layers=1,
            lookback_window=5,
            feature_dim=17,  # 15 features + 2 account info
            dropout=0.1
        )
        
        model = TransformerModel(model_config)
        
        # Simulate training episodes
        total_reward = 0
        num_episodes = 3
        
        for episode in range(num_episodes):
            obs, info = env.reset()
            episode_reward = 0
            done = False
            
            while not done:
                # Model prediction
                import torch
                action = model.predict(torch.tensor(obs).unsqueeze(0), deterministic=False)
                action_int = action.item()
                
                # Environment step
                obs, reward, terminated, truncated, info = env.step(action_int)
                done = terminated or truncated
                episode_reward += reward
                
            total_reward += episode_reward
            
        # Should complete without errors
        assert num_episodes == 3
        assert isinstance(total_reward, (int, float))
        
        env.close()


if __name__ == "__main__":
    import torch
    import pandas as pd
    pytest.main([__file__])
