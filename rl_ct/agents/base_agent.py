"""
Base agent interface for reinforcement learning.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, <PERSON>ple
import numpy as np
import torch

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class BaseAgent(ABC):
    """Base class for all RL agents."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize base agent.
        
        Args:
            config: Agent configuration
        """
        self.config = config or {}
        self.device = torch.device(
            self.config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        )
        
        # Training state
        self.is_training = True
        self.total_steps = 0
        self.episode_count = 0
        
        logger.info(f"Initialized {self.__class__.__name__} on device: {self.device}")
        
    @abstractmethod
    def predict(
        self,
        observation: np.ndarray,
        deterministic: bool = False
    ) -> Tuple[np.ndarray, Optional[Dict[str, Any]]]:
        """
        Predict action from observation.
        
        Args:
            observation: Environment observation
            deterministic: Whether to use deterministic policy
            
        Returns:
            Tuple of (action, extra_info)
        """
        pass
        
    @abstractmethod
    def learn(
        self,
        total_timesteps: int,
        callback: Optional[Any] = None,
        log_interval: int = 1000,
        eval_env: Optional[Any] = None,
        eval_freq: int = -1,
        n_eval_episodes: int = 5,
        tb_log_name: str = "run",
        eval_log_path: Optional[str] = None,
        reset_num_timesteps: bool = True,
    ) -> 'BaseAgent':
        """
        Train the agent.
        
        Args:
            total_timesteps: Total number of timesteps to train
            callback: Optional callback function
            log_interval: Logging interval
            eval_env: Evaluation environment
            eval_freq: Evaluation frequency
            n_eval_episodes: Number of evaluation episodes
            tb_log_name: Tensorboard log name
            eval_log_path: Evaluation log path
            reset_num_timesteps: Whether to reset timestep counter
            
        Returns:
            Self for method chaining
        """
        pass
        
    @abstractmethod
    def save(self, path: str) -> None:
        """
        Save agent to file.
        
        Args:
            path: Path to save agent
        """
        pass
        
    @abstractmethod
    def load(self, path: str) -> None:
        """
        Load agent from file.
        
        Args:
            path: Path to load agent from
        """
        pass
        
    def set_training_mode(self, training: bool = True) -> None:
        """
        Set training mode.
        
        Args:
            training: Whether to set training mode
        """
        self.is_training = training
        
    def get_parameters(self) -> Dict[str, Any]:
        """
        Get agent parameters.
        
        Returns:
            Dictionary of parameters
        """
        return {
            'config': self.config,
            'device': str(self.device),
            'total_steps': self.total_steps,
            'episode_count': self.episode_count,
            'is_training': self.is_training,
        }
        
    def reset(self) -> None:
        """Reset agent state."""
        self.total_steps = 0
        self.episode_count = 0
        
    def update_step_count(self, steps: int = 1) -> None:
        """
        Update step count.
        
        Args:
            steps: Number of steps to add
        """
        self.total_steps += steps
        
    def update_episode_count(self, episodes: int = 1) -> None:
        """
        Update episode count.
        
        Args:
            episodes: Number of episodes to add
        """
        self.episode_count += episodes
        
    def get_stats(self) -> Dict[str, Any]:
        """
        Get training statistics.
        
        Returns:
            Dictionary of statistics
        """
        return {
            'total_steps': self.total_steps,
            'episode_count': self.episode_count,
            'is_training': self.is_training,
        }
        
    def evaluate(
        self,
        env: Any,
        n_eval_episodes: int = 10,
        deterministic: bool = True,
        render: bool = False,
        callback: Optional[Any] = None,
        reward_threshold: Optional[float] = None,
        return_episode_rewards: bool = False,
    ) -> Tuple[float, float]:
        """
        Evaluate the agent.
        
        Args:
            env: Environment to evaluate on
            n_eval_episodes: Number of episodes to evaluate
            deterministic: Whether to use deterministic policy
            render: Whether to render environment
            callback: Optional callback
            reward_threshold: Optional reward threshold
            return_episode_rewards: Whether to return episode rewards
            
        Returns:
            Tuple of (mean_reward, std_reward)
        """
        episode_rewards = []
        episode_lengths = []
        
        for episode in range(n_eval_episodes):
            obs, _ = env.reset()
            episode_reward = 0.0
            episode_length = 0
            done = False
            
            while not done:
                action, _ = self.predict(obs, deterministic=deterministic)
                obs, reward, terminated, truncated, info = env.step(action)
                done = terminated or truncated
                
                episode_reward += reward
                episode_length += 1
                
                if render:
                    env.render()
                    
                if callback is not None:
                    callback(locals(), globals())
                    
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            
            if reward_threshold is not None and episode_reward < reward_threshold:
                logger.warning(f"Episode {episode} reward {episode_reward} below threshold {reward_threshold}")
                
        mean_reward = np.mean(episode_rewards)
        std_reward = np.std(episode_rewards)
        mean_length = np.mean(episode_lengths)
        
        logger.info(f"Evaluation over {n_eval_episodes} episodes:")
        logger.info(f"Mean reward: {mean_reward:.2f} ± {std_reward:.2f}")
        logger.info(f"Mean episode length: {mean_length:.1f}")
        
        if return_episode_rewards:
            return mean_reward, std_reward, episode_rewards
        else:
            return mean_reward, std_reward
