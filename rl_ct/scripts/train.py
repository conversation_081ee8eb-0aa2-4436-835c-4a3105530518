"""
Main training script for RL-CT framework.
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import ray
from ray import tune
from ray.tune.schedulers import ASHAScheduler
from ray.tune.stopper import TrialPlateauStopper

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from rl_ct.agents.ppo_agent import PPOAgent
from rl_ct.utils.config import load_config
from rl_ct.utils.logger import setup_logging, get_logger
from rl_ct.utils.metrics import TradingMetrics

logger = get_logger(__name__)


def train_single_run(
    config_path: str,
    experiment_name: Optional[str] = None,
    resume: bool = False,
    checkpoint_path: Optional[str] = None,
) -> str:
    """
    Train a single model run.
    
    Args:
        config_path: Path to training configuration
        experiment_name: Name of the experiment
        resume: Whether to resume from checkpoint
        checkpoint_path: Path to checkpoint to resume from
        
    Returns:
        Path to the best checkpoint
    """
    # Load configuration
    config = load_config(config_path)
    
    # Setup logging
    log_config = config.get('logging', {})
    setup_logging(
        level=log_config.get('level', 'INFO'),
        log_dir=log_config.get('log_dir', 'logs'),
        log_file=f"{experiment_name or 'training'}.log"
    )
    
    logger.info(f"Starting training with config: {config_path}")
    logger.info(f"Experiment name: {experiment_name}")
    
    # Initialize Ray
    ray_config = config.get('ray', {})
    if not ray.is_initialized():
        ray.init(
            local_mode=ray_config.get('local_mode', False),
            num_cpus=ray_config.get('resources', {}).get('cpu', 4),
            num_gpus=ray_config.get('resources', {}).get('gpu', 1),
            ignore_reinit_error=True
        )
    
    try:
        # Create agent
        agent = PPOAgent(config)
        
        # Resume from checkpoint if specified
        if resume and checkpoint_path:
            logger.info(f"Resuming from checkpoint: {checkpoint_path}")
            agent.load(checkpoint_path)
        
        # Training parameters
        training_config = config.get('training', {})
        total_timesteps = training_config.get('total_timesteps', 1000000)
        eval_freq = training_config.get('eval_freq', 10000)
        save_freq = training_config.get('save_freq', 50000)
        
        # Create checkpoint directory
        checkpoint_dir = Path("checkpoints") / (experiment_name or "default")
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # Training loop with periodic evaluation and saving
        best_reward = float('-inf')
        best_checkpoint = None
        
        for step in range(0, total_timesteps, save_freq):
            remaining_steps = min(save_freq, total_timesteps - step)
            
            # Train for this interval
            agent.learn(
                total_timesteps=remaining_steps,
                log_interval=training_config.get('log_interval', 1000),
                reset_num_timesteps=False
            )
            
            # Evaluate if needed
            if eval_freq > 0 and (step + remaining_steps) % eval_freq == 0:
                logger.info("Running evaluation...")
                
                # Create evaluation environment
                from rl_ct.envs import CryptoTradingEnv
                eval_env_config = config.get('environment', {}).copy()
                eval_env_config['regime'] = 'evaluation'
                eval_env = CryptoTradingEnv(eval_env_config)
                
                # Evaluate
                mean_reward, std_reward = agent.evaluate(
                    eval_env,
                    n_eval_episodes=training_config.get('eval_episodes', 10),
                    deterministic=True
                )
                
                logger.info(f"Evaluation - Mean reward: {mean_reward:.2f} ± {std_reward:.2f}")
                
                # Save best model
                if mean_reward > best_reward:
                    best_reward = mean_reward
                    best_checkpoint = checkpoint_dir / f"best_model_step_{step + remaining_steps}.zip"
                    agent.save(str(best_checkpoint))
                    logger.info(f"New best model saved: {best_checkpoint}")
                
                eval_env.close()
            
            # Save periodic checkpoint
            periodic_checkpoint = checkpoint_dir / f"model_step_{step + remaining_steps}.zip"
            agent.save(str(periodic_checkpoint))
            
        # Save final model
        final_checkpoint = checkpoint_dir / "final_model.zip"
        agent.save(str(final_checkpoint))
        
        logger.info("Training completed successfully")
        return str(best_checkpoint or final_checkpoint)
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise
    finally:
        # Cleanup
        if 'agent' in locals():
            agent.close()


def train_with_hyperparameter_tuning(
    config_path: str,
    experiment_name: Optional[str] = None,
    n_trials: int = 10,
) -> str:
    """
    Train with hyperparameter tuning using Ray Tune.
    
    Args:
        config_path: Path to training configuration
        experiment_name: Name of the experiment
        n_trials: Number of trials for hyperparameter tuning
        
    Returns:
        Path to the best checkpoint
    """
    # Load configuration
    config = load_config(config_path)
    
    logger.info(f"Starting hyperparameter tuning with {n_trials} trials")
    
    # Define search space
    search_space = {
        "algorithm.learning_rate": tune.loguniform(1e-5, 1e-2),
        "algorithm.clip_range": tune.uniform(0.1, 0.4),
        "algorithm.ent_coef": tune.loguniform(1e-3, 1e-1),
        "algorithm.gamma": tune.uniform(0.95, 0.999),
        "algorithm.gae_lambda": tune.uniform(0.9, 0.99),
        "model.hidden_size": tune.choice([128, 256, 512]),
        "model.num_layers": tune.choice([2, 3, 4]),
    }
    
    # Define training function for Ray Tune
    def train_fn(tune_config):
        # Merge tune config with base config
        merged_config = config.copy()
        for key, value in tune_config.items():
            keys = key.split('.')
            current = merged_config
            for k in keys[:-1]:
                current = current[k]
            current[keys[-1]] = value
        
        # Create agent
        agent = PPOAgent(merged_config)
        
        # Training parameters
        training_config = merged_config.get('training', {})
        total_timesteps = training_config.get('total_timesteps', 100000)  # Shorter for tuning
        eval_freq = training_config.get('eval_freq', 10000)
        
        # Training loop
        for step in range(0, total_timesteps, eval_freq):
            remaining_steps = min(eval_freq, total_timesteps - step)
            
            # Train
            agent.learn(
                total_timesteps=remaining_steps,
                reset_num_timesteps=False
            )
            
            # Evaluate
            from rl_ct.envs import CryptoTradingEnv
            eval_env_config = merged_config.get('environment', {}).copy()
            eval_env_config['regime'] = 'evaluation'
            eval_env = CryptoTradingEnv(eval_env_config)
            
            mean_reward, _ = agent.evaluate(
                eval_env,
                n_eval_episodes=5,
                deterministic=True
            )
            
            # Report to Ray Tune
            tune.report(mean_reward=mean_reward, step=step + remaining_steps)
            
            eval_env.close()
        
        agent.close()
    
    # Configure scheduler and stopper
    scheduler = ASHAScheduler(
        metric="mean_reward",
        mode="max",
        max_t=config.get('training', {}).get('total_timesteps', 100000),
        grace_period=10000,
        reduction_factor=2
    )
    
    stopper = TrialPlateauStopper(
        metric="mean_reward",
        std=0.01,
        num_results=4,
        grace_period=20000,
        mode="max"
    )
    
    # Run hyperparameter tuning
    analysis = tune.run(
        train_fn,
        config=search_space,
        num_samples=n_trials,
        scheduler=scheduler,
        stop=stopper,
        resources_per_trial={"cpu": 2, "gpu": 0},
        local_dir="ray_results",
        name=experiment_name or "hyperparameter_tuning",
        verbose=1
    )
    
    # Get best trial
    best_trial = analysis.get_best_trial("mean_reward", "max")
    logger.info(f"Best trial config: {best_trial.config}")
    logger.info(f"Best trial result: {best_trial.last_result}")
    
    return best_trial.checkpoint.value


def main(
    config_path: str = "configs/training/default.yaml",
    experiment_name: Optional[str] = None,
    resume: bool = False,
    checkpoint_path: Optional[str] = None,
    hyperparameter_tuning: bool = False,
    n_trials: int = 10,
) -> None:
    """
    Main training function.
    
    Args:
        config_path: Path to training configuration
        experiment_name: Name of the experiment
        resume: Whether to resume from checkpoint
        checkpoint_path: Path to checkpoint to resume from
        hyperparameter_tuning: Whether to use hyperparameter tuning
        n_trials: Number of trials for hyperparameter tuning
    """
    try:
        if hyperparameter_tuning:
            best_checkpoint = train_with_hyperparameter_tuning(
                config_path=config_path,
                experiment_name=experiment_name,
                n_trials=n_trials
            )
        else:
            best_checkpoint = train_single_run(
                config_path=config_path,
                experiment_name=experiment_name,
                resume=resume,
                checkpoint_path=checkpoint_path
            )
        
        logger.info(f"Training completed. Best checkpoint: {best_checkpoint}")
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise
    finally:
        # Cleanup Ray
        if ray.is_initialized():
            ray.shutdown()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Train RL-CT model")
    parser.add_argument("--config", "-c", default="configs/training/default.yaml",
                       help="Path to training configuration")
    parser.add_argument("--name", "-n", help="Experiment name")
    parser.add_argument("--resume", "-r", action="store_true",
                       help="Resume from checkpoint")
    parser.add_argument("--checkpoint", help="Path to checkpoint to resume from")
    parser.add_argument("--tune", action="store_true",
                       help="Use hyperparameter tuning")
    parser.add_argument("--trials", type=int, default=10,
                       help="Number of trials for hyperparameter tuning")
    
    args = parser.parse_args()
    
    main(
        config_path=args.config,
        experiment_name=args.name,
        resume=args.resume,
        checkpoint_path=args.checkpoint,
        hyperparameter_tuning=args.tune,
        n_trials=args.trials
    )
