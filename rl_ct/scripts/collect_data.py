"""
Data collection script for cryptocurrency market data.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from rl_ct.utils.data_loaders import MarketDataLoader
from rl_ct.utils.preprocessing import DataProcessor
from rl_ct.utils.logger import setup_logging, get_logger

logger = get_logger(__name__)


class DataCollector:
    """Cryptocurrency data collector."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize data collector.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Market data loader
        self.market_loader = MarketDataLoader(
            exchange=self.config.get('exchange', 'binance'),
            timeframe=self.config.get('timeframe', '1h'),
            config=self.config.get('market_config', {})
        )
        
        # Data processor
        self.processor = DataProcessor(self.config.get('processing', {}))
        
        logger.info("DataCollector initialized")
        
    def collect_historical_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        output_dir: str = "data/raw"
    ) -> Dict[str, pd.DataFrame]:
        """
        Collect historical data for symbols.
        
        Args:
            symbols: List of trading symbols
            start_date: Start date for data collection
            end_date: End date for data collection
            output_dir: Output directory for raw data
            
        Returns:
            Dictionary of symbol -> DataFrame
        """
        logger.info(f"Collecting historical data for {len(symbols)} symbols")
        logger.info(f"Date range: {start_date} to {end_date}")
        
        collected_data = {}
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for symbol in symbols:
            try:
                logger.info(f"Collecting data for {symbol}")
                
                # Fetch historical data
                df = self.market_loader.get_historical_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if df.empty:
                    logger.warning(f"No data collected for {symbol}")
                    continue
                    
                # Save raw data
                symbol_clean = symbol.replace('/', '_')
                raw_file = output_path / f"{symbol_clean}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"
                df.to_csv(raw_file)
                
                collected_data[symbol] = df
                logger.info(f"Collected {len(df)} records for {symbol}")
                
            except Exception as e:
                logger.error(f"Failed to collect data for {symbol}: {e}")
                continue
                
        logger.info(f"Data collection completed. Collected data for {len(collected_data)} symbols")
        return collected_data
        
    def collect_latest_data(
        self,
        symbols: List[str],
        count: int = 1000,
        output_dir: str = "data/raw"
    ) -> Dict[str, pd.DataFrame]:
        """
        Collect latest data for symbols.
        
        Args:
            symbols: List of trading symbols
            count: Number of latest candles to collect
            output_dir: Output directory for raw data
            
        Returns:
            Dictionary of symbol -> DataFrame
        """
        logger.info(f"Collecting latest {count} candles for {len(symbols)} symbols")
        
        collected_data = {}
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for symbol in symbols:
            try:
                logger.info(f"Collecting latest data for {symbol}")
                
                # Fetch latest data
                df = self.market_loader.fetch_latest_data(symbol=symbol, count=count)
                
                if df.empty:
                    logger.warning(f"No data collected for {symbol}")
                    continue
                    
                # Save raw data
                symbol_clean = symbol.replace('/', '_')
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                raw_file = output_path / f"{symbol_clean}_latest_{timestamp}.csv"
                df.to_csv(raw_file)
                
                collected_data[symbol] = df
                logger.info(f"Collected {len(df)} records for {symbol}")
                
            except Exception as e:
                logger.error(f"Failed to collect data for {symbol}: {e}")
                continue
                
        logger.info(f"Latest data collection completed. Collected data for {len(collected_data)} symbols")
        return collected_data
        
    def process_and_save_data(
        self,
        raw_data: Dict[str, pd.DataFrame],
        dataset_name: str,
        output_dir: str = "data/processed"
    ) -> None:
        """
        Process raw data and save as training dataset.
        
        Args:
            raw_data: Dictionary of raw market data
            dataset_name: Name for the processed dataset
            output_dir: Output directory for processed data
        """
        logger.info(f"Processing data for dataset: {dataset_name}")
        
        if not raw_data:
            logger.error("No raw data to process")
            return
            
        # Combine data from all symbols
        combined_data = []
        
        for symbol, df in raw_data.items():
            # Add symbol column
            df_copy = df.copy()
            df_copy['symbol'] = symbol
            combined_data.append(df_copy)
            
        # Concatenate all data
        full_df = pd.concat(combined_data, ignore_index=True)
        full_df = full_df.sort_values('timestamp').reset_index(drop=True)
        
        logger.info(f"Combined data shape: {full_df.shape}")
        
        # Process data using DataProcessor
        try:
            # Create features
            processed_df = self.processor.create_features(full_df)
            
            # Scale data
            scaled_features = self.processor.scale_data(processed_df)
            
            # Extract price data
            price_columns = ['open', 'high', 'low', 'close']
            available_price_columns = [col for col in price_columns if col in full_df.columns]
            
            if available_price_columns:
                price_data = full_df[available_price_columns].values
            else:
                price_data = full_df[['close']].values if 'close' in full_df.columns else np.array([])
                
            # Save processed data
            self.processor.save_processed_data(output_dir, dataset_name)
            
            logger.info(f"Processed data saved to {output_dir}/{dataset_name}")
            
        except Exception as e:
            logger.error(f"Failed to process data: {e}")
            raise
            
    def create_train_test_splits(
        self,
        dataset_name: str,
        data_dir: str = "data/processed",
        train_ratio: float = 0.8,
        val_ratio: float = 0.1
    ) -> None:
        """
        Create train/validation/test splits from processed data.
        
        Args:
            dataset_name: Name of the dataset
            data_dir: Directory containing processed data
            train_ratio: Ratio of data for training
            val_ratio: Ratio of data for validation
        """
        logger.info(f"Creating train/test splits for dataset: {dataset_name}")
        
        # Load processed data
        from rl_ct.utils.data_loaders import DiskDataLoader
        
        loader = DiskDataLoader(
            data_dir=data_dir,
            dataset_name=dataset_name,
            file_format='npy'
        )
        
        price_data, feature_data = loader.load_data()
        
        if price_data.size == 0 or feature_data.size == 0:
            logger.error("No data found to split")
            return
            
        # Calculate split indices
        total_samples = len(price_data)
        train_end = int(total_samples * train_ratio)
        val_end = int(total_samples * (train_ratio + val_ratio))
        
        # Create splits
        train_price = price_data[:train_end]
        train_features = feature_data[:train_end]
        
        val_price = price_data[train_end:val_end]
        val_features = feature_data[train_end:val_end]
        
        test_price = price_data[val_end:]
        test_features = feature_data[val_end:]
        
        # Save splits
        splits = {
            'train': (train_price, train_features),
            'val': (val_price, val_features),
            'test': (test_price, test_features)
        }
        
        for split_name, (price, features) in splits.items():
            split_loader = DiskDataLoader(
                data_dir=data_dir,
                dataset_name=f"{dataset_name}_{split_name}",
                file_format='npy'
            )
            
            metadata = {
                'split': split_name,
                'original_dataset': dataset_name,
                'samples': len(price),
                'price_shape': price.shape,
                'feature_shape': features.shape,
            }
            
            split_loader.save_data(price, features, metadata)
            
        logger.info(f"Created splits - Train: {len(train_price)}, Val: {len(val_price)}, Test: {len(test_price)}")


def main(
    symbols: List[str],
    timeframe: str = "1h",
    days: int = 30,
    output_dir: str = "data/raw",
    process_data: bool = True,
    dataset_name: str = "default",
    create_splits: bool = True,
    exchange: str = "binance"
) -> None:
    """
    Main data collection function.
    
    Args:
        symbols: List of trading symbols
        timeframe: Timeframe for data collection
        days: Number of days to collect
        output_dir: Output directory for data
        process_data: Whether to process the collected data
        dataset_name: Name for the processed dataset
        create_splits: Whether to create train/test splits
        exchange: Exchange to collect data from
    """
    # Setup logging
    setup_logging(level='INFO', log_dir='logs', log_file='data_collection.log')
    
    try:
        # Create data collector
        config = {
            'exchange': exchange,
            'timeframe': timeframe,
            'processing': {
                'scaler': {
                    'type': 'quantile',
                    'min_quantile': 0.5,
                    'max_quantile': 99.5,
                    'scale_coef': 10000.0
                }
            }
        }
        
        collector = DataCollector(config)
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Collect data
        raw_data = collector.collect_historical_data(
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            output_dir=output_dir
        )
        
        if not raw_data:
            logger.error("No data collected")
            return
            
        # Process data if requested
        if process_data:
            collector.process_and_save_data(
                raw_data=raw_data,
                dataset_name=dataset_name,
                output_dir="data/processed"
            )
            
            # Create splits if requested
            if create_splits:
                collector.create_train_test_splits(
                    dataset_name=dataset_name,
                    data_dir="data/processed"
                )
                
        logger.info("Data collection completed successfully")
        
    except Exception as e:
        logger.error(f"Data collection failed: {e}")
        raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Collect cryptocurrency market data")
    parser.add_argument("--symbols", "-s", default="BTC/USDT,ETH/USDT",
                       help="Comma-separated list of trading symbols")
    parser.add_argument("--timeframe", "-t", default="1h",
                       help="Timeframe for data collection")
    parser.add_argument("--days", "-d", type=int, default=30,
                       help="Number of days to collect")
    parser.add_argument("--output", "-o", default="data/raw",
                       help="Output directory for raw data")
    parser.add_argument("--dataset-name", default="default",
                       help="Name for the processed dataset")
    parser.add_argument("--exchange", default="binance",
                       help="Exchange to collect data from")
    parser.add_argument("--no-process", action="store_true",
                       help="Skip data processing")
    parser.add_argument("--no-splits", action="store_true",
                       help="Skip creating train/test splits")
    
    args = parser.parse_args()
    
    symbols = [s.strip() for s in args.symbols.split(',')]
    
    main(
        symbols=symbols,
        timeframe=args.timeframe,
        days=args.days,
        output_dir=args.output,
        process_data=not args.no_process,
        dataset_name=args.dataset_name,
        create_splits=not args.no_splits,
        exchange=args.exchange
    )
