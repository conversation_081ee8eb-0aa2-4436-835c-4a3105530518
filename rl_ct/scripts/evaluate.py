"""
Model evaluation script for RL-CT framework.
"""

import os
import sys
import json
from pathlib import Path
from typing import Optional, Dict, Any, List
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from rl_ct.agents.ppo_agent import PPOAgent
from rl_ct.envs import CryptoTradingEnv
from rl_ct.utils.config import load_config
from rl_ct.utils.logger import setup_logging, get_logger
from rl_ct.utils.metrics import TradingMetrics

logger = get_logger(__name__)


class ModelEvaluator:
    """Model evaluation and analysis."""
    
    def __init__(self, model_path: str, config_path: str):
        """
        Initialize evaluator.
        
        Args:
            model_path: Path to trained model
            config_path: Path to configuration file
        """
        self.model_path = model_path
        self.config = load_config(config_path)
        
        # Load agent
        self.agent = PPOAgent(self.config)
        self.agent.load(model_path)
        
        # Create evaluation environment
        env_config = self.config.get('environment', {}).copy()
        env_config['regime'] = 'evaluation'
        self.env = CryptoTradingEnv(env_config)
        
        # Metrics tracker
        self.metrics = TradingMetrics()
        
        logger.info(f"ModelEvaluator initialized with model: {model_path}")
        
    def evaluate_episodes(
        self,
        n_episodes: int = 10,
        deterministic: bool = True,
        render: bool = False
    ) -> Dict[str, Any]:
        """
        Evaluate model over multiple episodes.
        
        Args:
            n_episodes: Number of episodes to evaluate
            deterministic: Whether to use deterministic policy
            render: Whether to render environment
            
        Returns:
            Evaluation results
        """
        logger.info(f"Evaluating model over {n_episodes} episodes")
        
        episode_results = []
        all_rewards = []
        all_actions = []
        all_balances = []
        all_positions = []
        
        for episode in range(n_episodes):
            obs, _ = self.env.reset()
            episode_reward = 0.0
            episode_length = 0
            episode_actions = []
            episode_balances = []
            episode_positions = []
            done = False
            
            while not done:
                # Predict action
                action, _ = self.agent.predict(obs, deterministic=deterministic)
                
                # Take step
                obs, reward, terminated, truncated, info = self.env.step(action[0])
                done = terminated or truncated
                
                # Track metrics
                episode_reward += reward
                episode_length += 1
                episode_actions.append(action[0])
                episode_balances.append(info.get('balance', 0))
                episode_positions.append(info.get('position_long', 0) - info.get('position_short', 0))
                
                if render:
                    self.env.render()
                    
            # Store episode results
            episode_results.append({
                'episode': episode,
                'reward': episode_reward,
                'length': episode_length,
                'final_balance': episode_balances[-1] if episode_balances else 0,
                'total_trades': info.get('total_trades', 0),
                'win_rate': info.get('win_rate', 0),
                'max_drawdown': info.get('max_drawdown', 0),
                'sharpe_ratio': info.get('sharpe_ratio', 0),
            })
            
            all_rewards.append(episode_reward)
            all_actions.extend(episode_actions)
            all_balances.extend(episode_balances)
            all_positions.extend(episode_positions)
            
            logger.info(f"Episode {episode + 1}/{n_episodes} - Reward: {episode_reward:.2f}")
        
        # Calculate summary statistics
        results = {
            'n_episodes': n_episodes,
            'mean_reward': np.mean(all_rewards),
            'std_reward': np.std(all_rewards),
            'min_reward': np.min(all_rewards),
            'max_reward': np.max(all_rewards),
            'mean_final_balance': np.mean([r['final_balance'] for r in episode_results]),
            'mean_win_rate': np.mean([r['win_rate'] for r in episode_results]),
            'mean_max_drawdown': np.mean([r['max_drawdown'] for r in episode_results]),
            'mean_sharpe_ratio': np.mean([r['sharpe_ratio'] for r in episode_results]),
            'action_distribution': self._analyze_actions(all_actions),
            'episode_results': episode_results,
            'all_rewards': all_rewards,
            'all_balances': all_balances,
            'all_positions': all_positions,
        }
        
        logger.info(f"Evaluation completed - Mean reward: {results['mean_reward']:.2f} ± {results['std_reward']:.2f}")
        
        return results
        
    def _analyze_actions(self, actions: List[int]) -> Dict[str, float]:
        """Analyze action distribution."""
        action_counts = {}
        action_names = {0: 'hold', 1: 'buy', 2: 'sell', 3: 'close'}
        
        for action in actions:
            name = action_names.get(action, f'action_{action}')
            action_counts[name] = action_counts.get(name, 0) + 1
            
        total_actions = len(actions)
        action_distribution = {
            name: count / total_actions for name, count in action_counts.items()
        }
        
        return action_distribution
        
    def create_visualizations(
        self,
        results: Dict[str, Any],
        output_dir: str
    ) -> None:
        """
        Create evaluation visualizations.
        
        Args:
            results: Evaluation results
            output_dir: Output directory for plots
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 1. Reward distribution
        plt.figure(figsize=(10, 6))
        plt.hist(results['all_rewards'], bins=20, alpha=0.7, edgecolor='black')
        plt.axvline(results['mean_reward'], color='red', linestyle='--', 
                   label=f'Mean: {results["mean_reward"]:.2f}')
        plt.xlabel('Episode Reward')
        plt.ylabel('Frequency')
        plt.title('Distribution of Episode Rewards')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'reward_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Balance evolution
        if results['all_balances']:
            plt.figure(figsize=(12, 6))
            plt.plot(results['all_balances'], alpha=0.8)
            plt.xlabel('Time Steps')
            plt.ylabel('Balance')
            plt.title('Portfolio Balance Evolution')
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(output_path / 'balance_evolution.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        # 3. Position evolution
        if results['all_positions']:
            plt.figure(figsize=(12, 6))
            plt.plot(results['all_positions'], alpha=0.8)
            plt.xlabel('Time Steps')
            plt.ylabel('Net Position')
            plt.title('Position Evolution')
            plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(output_path / 'position_evolution.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        # 4. Action distribution
        action_dist = results['action_distribution']
        plt.figure(figsize=(8, 6))
        actions = list(action_dist.keys())
        frequencies = list(action_dist.values())
        plt.bar(actions, frequencies, alpha=0.8)
        plt.xlabel('Action')
        plt.ylabel('Frequency')
        plt.title('Action Distribution')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'action_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 5. Episode metrics
        episode_results = results['episode_results']
        if episode_results:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # Rewards
            episodes = [r['episode'] for r in episode_results]
            rewards = [r['reward'] for r in episode_results]
            axes[0, 0].plot(episodes, rewards, marker='o')
            axes[0, 0].set_title('Episode Rewards')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Reward')
            axes[0, 0].grid(True, alpha=0.3)
            
            # Final balances
            balances = [r['final_balance'] for r in episode_results]
            axes[0, 1].plot(episodes, balances, marker='o', color='green')
            axes[0, 1].set_title('Final Balances')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('Balance')
            axes[0, 1].grid(True, alpha=0.3)
            
            # Win rates
            win_rates = [r['win_rate'] for r in episode_results]
            axes[1, 0].plot(episodes, win_rates, marker='o', color='orange')
            axes[1, 0].set_title('Win Rates')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('Win Rate')
            axes[1, 0].grid(True, alpha=0.3)
            
            # Max drawdowns
            drawdowns = [r['max_drawdown'] for r in episode_results]
            axes[1, 1].plot(episodes, drawdowns, marker='o', color='red')
            axes[1, 1].set_title('Max Drawdowns')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Max Drawdown')
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(output_path / 'episode_metrics.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        logger.info(f"Visualizations saved to {output_path}")
        
    def save_results(
        self,
        results: Dict[str, Any],
        output_dir: str
    ) -> None:
        """
        Save evaluation results to files.
        
        Args:
            results: Evaluation results
            output_dir: Output directory
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Save summary statistics
        summary = {
            'model_path': self.model_path,
            'n_episodes': results['n_episodes'],
            'mean_reward': results['mean_reward'],
            'std_reward': results['std_reward'],
            'min_reward': results['min_reward'],
            'max_reward': results['max_reward'],
            'mean_final_balance': results['mean_final_balance'],
            'mean_win_rate': results['mean_win_rate'],
            'mean_max_drawdown': results['mean_max_drawdown'],
            'mean_sharpe_ratio': results['mean_sharpe_ratio'],
            'action_distribution': results['action_distribution'],
        }
        
        with open(output_path / 'evaluation_summary.json', 'w') as f:
            json.dump(summary, f, indent=2)
            
        # Save detailed episode results
        episode_df = pd.DataFrame(results['episode_results'])
        episode_df.to_csv(output_path / 'episode_results.csv', index=False)
        
        logger.info(f"Results saved to {output_path}")
        
    def close(self) -> None:
        """Close evaluator and cleanup resources."""
        if hasattr(self, 'env'):
            self.env.close()
        if hasattr(self, 'agent'):
            self.agent.close()


def main(
    model_path: str,
    config_path: str = "configs/environment/default.yaml",
    output_dir: str = "results/evaluation",
    n_episodes: int = 10,
    deterministic: bool = True,
    create_plots: bool = True,
) -> None:
    """
    Main evaluation function.
    
    Args:
        model_path: Path to trained model
        config_path: Path to configuration file
        output_dir: Output directory for results
        n_episodes: Number of episodes to evaluate
        deterministic: Whether to use deterministic policy
        create_plots: Whether to create visualization plots
    """
    # Setup logging
    setup_logging(level='INFO', log_dir='logs', log_file='evaluation.log')
    
    try:
        # Create evaluator
        evaluator = ModelEvaluator(model_path, config_path)
        
        # Run evaluation
        results = evaluator.evaluate_episodes(
            n_episodes=n_episodes,
            deterministic=deterministic
        )
        
        # Save results
        evaluator.save_results(results, output_dir)
        
        # Create visualizations
        if create_plots:
            evaluator.create_visualizations(results, output_dir)
        
        logger.info("Evaluation completed successfully")
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        raise
    finally:
        if 'evaluator' in locals():
            evaluator.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Evaluate RL-CT model")
    parser.add_argument("model_path", help="Path to trained model")
    parser.add_argument("--config", "-c", default="configs/environment/default.yaml",
                       help="Path to environment configuration")
    parser.add_argument("--output", "-o", default="results/evaluation",
                       help="Output directory for results")
    parser.add_argument("--episodes", "-e", type=int, default=10,
                       help="Number of episodes to evaluate")
    parser.add_argument("--stochastic", action="store_true",
                       help="Use stochastic policy (default: deterministic)")
    parser.add_argument("--no-plots", action="store_true",
                       help="Skip creating visualization plots")
    
    args = parser.parse_args()
    
    main(
        model_path=args.model_path,
        config_path=args.config,
        output_dir=args.output,
        n_episodes=args.episodes,
        deterministic=not args.stochastic,
        create_plots=not args.no_plots
    )
