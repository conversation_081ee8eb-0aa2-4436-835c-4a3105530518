"""
Base model interface for neural networks.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
import torch
import torch.nn as nn
from dataclasses import dataclass

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ModelConfig:
    """Base configuration for models."""
    
    # Architecture
    input_size: int = 100
    hidden_size: int = 256
    output_size: int = 3
    num_layers: int = 3
    dropout: float = 0.1
    activation: str = "relu"
    
    # Training
    learning_rate: float = 3e-4
    weight_decay: float = 1e-5
    gradient_clip: float = 1.0
    
    # Device
    device: str = "auto"
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"


class BaseModel(nn.Module, ABC):
    """Base class for all neural network models."""
    
    def __init__(self, config: ModelConfig):
        """
        Initialize base model.
        
        Args:
            config: Model configuration
        """
        super().__init__()
        self.config = config
        self.device = torch.device(config.device)
        
        # Model components (to be defined by subclasses)
        self.encoder: Optional[nn.Module] = None
        self.policy_head: Optional[nn.Module] = None
        self.value_head: Optional[nn.Module] = None
        
        logger.info(f"Initialized {self.__class__.__name__} on device: {self.device}")
        
    @abstractmethod
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the model.
        
        Args:
            x: Input tensor
            
        Returns:
            Tuple of (policy_logits, value)
        """
        pass
        
    def get_action_distribution(self, x: torch.Tensor) -> torch.distributions.Distribution:
        """
        Get action distribution from model output.
        
        Args:
            x: Input tensor
            
        Returns:
            Action distribution
        """
        policy_logits, _ = self.forward(x)
        return torch.distributions.Categorical(logits=policy_logits)
        
    def get_value(self, x: torch.Tensor) -> torch.Tensor:
        """
        Get value estimate from model.
        
        Args:
            x: Input tensor
            
        Returns:
            Value tensor
        """
        _, value = self.forward(x)
        return value
        
    def predict(self, x: torch.Tensor, deterministic: bool = False) -> torch.Tensor:
        """
        Predict action from input.
        
        Args:
            x: Input tensor
            deterministic: Whether to use deterministic policy
            
        Returns:
            Action tensor
        """
        with torch.no_grad():
            dist = self.get_action_distribution(x)
            if deterministic:
                action = dist.probs.argmax(dim=-1)
            else:
                action = dist.sample()
        return action
        
    def evaluate_actions(
        self,
        x: torch.Tensor,
        actions: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Evaluate actions for training.
        
        Args:
            x: Input tensor
            actions: Action tensor
            
        Returns:
            Tuple of (log_probs, values, entropy)
        """
        policy_logits, values = self.forward(x)
        dist = torch.distributions.Categorical(logits=policy_logits)
        
        log_probs = dist.log_prob(actions)
        entropy = dist.entropy()
        
        return log_probs, values, entropy
        
    def save(self, path: str) -> None:
        """
        Save model to file.
        
        Args:
            path: Path to save model
        """
        torch.save({
            'model_state_dict': self.state_dict(),
            'config': self.config,
        }, path)
        logger.info(f"Model saved to {path}")
        
    @classmethod
    def load(cls, path: str, config: Optional[ModelConfig] = None) -> 'BaseModel':
        """
        Load model from file.
        
        Args:
            path: Path to load model from
            config: Optional config override
            
        Returns:
            Loaded model
        """
        checkpoint = torch.load(path, map_location='cpu', weights_only=False)
        
        if config is None:
            config = checkpoint['config']
            
        model = cls(config)
        model.load_state_dict(checkpoint['model_state_dict'])
        
        logger.info(f"Model loaded from {path}")
        return model
        
    def get_num_parameters(self) -> int:
        """
        Get total number of parameters.
        
        Returns:
            Number of parameters
        """
        return sum(p.numel() for p in self.parameters())
        
    def get_num_trainable_parameters(self) -> int:
        """
        Get number of trainable parameters.
        
        Returns:
            Number of trainable parameters
        """
        return sum(p.numel() for p in self.parameters() if p.requires_grad)
        
    def freeze_encoder(self) -> None:
        """Freeze encoder parameters."""
        if self.encoder is not None:
            for param in self.encoder.parameters():
                param.requires_grad = False
            logger.info("Encoder parameters frozen")
            
    def unfreeze_encoder(self) -> None:
        """Unfreeze encoder parameters."""
        if self.encoder is not None:
            for param in self.encoder.parameters():
                param.requires_grad = True
            logger.info("Encoder parameters unfrozen")
            
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get model information.
        
        Returns:
            Dictionary with model info
        """
        return {
            'model_type': self.__class__.__name__,
            'config': self.config,
            'device': str(self.device),
            'num_parameters': self.get_num_parameters(),
            'num_trainable_parameters': self.get_num_trainable_parameters(),
        }
        
    def to_device(self, device: Optional[str] = None) -> 'BaseModel':
        """
        Move model to device.
        
        Args:
            device: Target device
            
        Returns:
            Self for method chaining
        """
        if device is not None:
            self.device = torch.device(device)
            self.config.device = device
            
        self.to(self.device)
        logger.info(f"Model moved to device: {self.device}")
        return self
        
    def summary(self) -> None:
        """Print model summary."""
        print(f"\n{self.__class__.__name__} Summary:")
        print(f"{'='*50}")
        print(f"Input size: {self.config.input_size}")
        print(f"Hidden size: {self.config.hidden_size}")
        print(f"Output size: {self.config.output_size}")
        print(f"Number of layers: {self.config.num_layers}")
        print(f"Dropout: {self.config.dropout}")
        print(f"Activation: {self.config.activation}")
        print(f"Device: {self.device}")
        print(f"Total parameters: {self.get_num_parameters():,}")
        print(f"Trainable parameters: {self.get_num_trainable_parameters():,}")
        print(f"{'='*50}\n")


def get_activation_function(activation: str) -> nn.Module:
    """
    Get activation function by name.
    
    Args:
        activation: Activation function name
        
    Returns:
        Activation function module
    """
    activations = {
        'relu': nn.ReLU(),
        'gelu': nn.GELU(),
        'tanh': nn.Tanh(),
        'sigmoid': nn.Sigmoid(),
        'leaky_relu': nn.LeakyReLU(),
        'elu': nn.ELU(),
        'swish': nn.SiLU(),
    }
    
    if activation.lower() not in activations:
        raise ValueError(f"Unknown activation function: {activation}")
        
    return activations[activation.lower()]


def initialize_weights(module: nn.Module, method: str = "xavier_uniform") -> None:
    """
    Initialize model weights.
    
    Args:
        module: Module to initialize
        method: Initialization method
    """
    if isinstance(module, (nn.Linear, nn.Conv1d, nn.Conv2d)):
        if method == "xavier_uniform":
            nn.init.xavier_uniform_(module.weight)
        elif method == "xavier_normal":
            nn.init.xavier_normal_(module.weight)
        elif method == "kaiming_uniform":
            nn.init.kaiming_uniform_(module.weight)
        elif method == "kaiming_normal":
            nn.init.kaiming_normal_(module.weight)
        else:
            raise ValueError(f"Unknown initialization method: {method}")
            
        if module.bias is not None:
            nn.init.constant_(module.bias, 0)
    elif isinstance(module, (nn.LayerNorm, nn.BatchNorm1d)):
        nn.init.constant_(module.weight, 1)
        nn.init.constant_(module.bias, 0)
