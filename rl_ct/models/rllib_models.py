"""
RLlib model adapters for custom models.
"""

from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import torch
import torch.nn as nn
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.models.modelv2 import ModelV2
from ray.rllib.utils.annotations import override
from ray.rllib.utils.typing import TensorType

from rl_ct.models.transformer import TransformerModel, TransformerConfig
from rl_ct.models.mlp import MLPModel, MLPConfig
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class RLlibTransformerModel(TorchModelV2, nn.Module):
    """RLlib adapter for Transformer model."""
    
    def __init__(
        self,
        obs_space,
        action_space,
        num_outputs: int,
        model_config: Dict[str, Any],
        name: str,
        **kwargs
    ):
        """
        Initialize RLlib Transformer model.
        
        Args:
            obs_space: Observation space
            action_space: Action space
            num_outputs: Number of action outputs
            model_config: Model configuration
            name: Model name
            **kwargs: Additional arguments
        """
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        # Extract configuration
        custom_config = model_config.get("custom_model_config", {})

        # Determine device based on RLlib configuration
        # If num_gpus > 0 in the main config, use CUDA, otherwise CPU
        device = self._determine_device(custom_config)
        input_size = int(np.prod(obs_space.shape))
        lookback_window = custom_config.get('lookback_window', 168)
        feature_dim = custom_config.get('feature_dim', None)
        # Calculate feature dimension if not provided
        feature_dim = feature_dim if feature_dim is not None else input_size // lookback_window
        
        # Create transformer configuration
        self.config = TransformerConfig(
            # Input/output dimensions
            input_size=input_size,
            output_size=num_outputs,

            # Architecture parameters
            d_model=custom_config.get('d_model', 256),
            num_heads=custom_config.get('num_heads', 8),
            d_ff=custom_config.get('d_ff', 512),
            num_encoder_layers=custom_config.get('num_layers', 3),
            dropout=custom_config.get('dropout', 0.1),
            activation=custom_config.get('activation', 'gelu'),

            # Sequence parameters
            lookback_window=lookback_window,
            feature_dim=feature_dim,
            max_seq_length=custom_config.get('max_seq_length', 168),
            use_positional_encoding=custom_config.get('use_positional_encoding', True),

            # Training parameters
            learning_rate=custom_config.get('learning_rate', 3e-4),
            weight_decay=custom_config.get('weight_decay', 1e-5),
            gradient_clip=custom_config.get('gradient_clip', 1.0),

            # Device configuration
            device=device,
        )
        
        # Create the transformer model
        self.model = TransformerModel(self.config)
        
        # Value function output will be handled by the model
        self._value_out = None
        
        logger.info(f"RLlibTransformerModel initialized with {self.model.get_num_parameters():,} parameters")

    def _determine_device(self, custom_config: Dict[str, Any]) -> str:
        """
        Determine the appropriate device based on configuration.

        Args:
            custom_config: Custom model configuration

        Returns:
            Device string ('cpu' or 'cuda')
        """
        # Check if device is explicitly set in config
        if 'device' in custom_config:
            return custom_config['device']

        # For RLlib compatibility, default to CPU unless explicitly configured for GPU
        # This avoids device mismatch issues
        return 'cpu'

    @override(ModelV2)
    def forward(
        self,
        input_dict: Dict[str, TensorType],
        state: List[TensorType],
        seq_lens: TensorType,
    ) -> Tuple[TensorType, List[TensorType]]:
        """
        Forward pass through the model.

        Args:
            input_dict: Input dictionary containing observations
            state: RNN state (not used for transformer)
            seq_lens: Sequence lengths (not used)

        Returns:
            Tuple of (action_logits, new_state)
        """
        obs = input_dict["obs"]

        # Ensure input tensor is on the same device as the model
        if obs.device != next(self.model.parameters()).device:
            obs = obs.to(next(self.model.parameters()).device)

        # Forward pass through transformer
        action_logits, value = self.model(obs)

        # Store value for value_function method
        self._value_out = value

        return action_logits, state
        
    @override(ModelV2)
    def value_function(self) -> TensorType:
        """
        Return the value function output.
        
        Returns:
            Value function output
        """
        if self._value_out is None:
            return torch.zeros(1)
        return self._value_out


class RLlibMLPModel(TorchModelV2, nn.Module):
    """RLlib adapter for MLP model."""
    
    def __init__(
        self,
        obs_space,
        action_space,
        num_outputs: int,
        model_config: Dict[str, Any],
        name: str,
        **kwargs
    ):
        """
        Initialize RLlib MLP model.
        
        Args:
            obs_space: Observation space
            action_space: Action space
            num_outputs: Number of action outputs
            model_config: Model configuration
            name: Model name
            **kwargs: Additional arguments
        """
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        # Extract configuration
        custom_config = model_config.get("custom_model_config", {})

        # Determine device based on RLlib configuration
        device = self._determine_device(custom_config)

        # Create MLP configuration
        self.config = MLPConfig(
            # Input/output dimensions
            input_size=int(np.prod(obs_space.shape)),
            output_size=num_outputs,

            # Architecture parameters
            hidden_size=custom_config.get('hidden_size', 256),
            hidden_layers=custom_config.get('hidden_layers', [256, 256]),
            num_layers=custom_config.get('num_layers', 3),
            dropout=custom_config.get('dropout', 0.1),
            activation=custom_config.get('activation', 'relu'),

            # Normalization
            use_batch_norm=custom_config.get('use_batch_norm', False),
            use_layer_norm=custom_config.get('use_layer_norm', True),

            # Training parameters
            learning_rate=custom_config.get('learning_rate', 3e-4),
            weight_decay=custom_config.get('weight_decay', 1e-5),
            gradient_clip=custom_config.get('gradient_clip', 1.0),

            # Device configuration - force CPU for RLlib compatibility
            device=custom_config.get('device', 'cpu'),
        )
        
        # Create the MLP model
        self.model = MLPModel(self.config)
        
        # Value function output will be handled by the model
        self._value_out = None
        
        logger.info(f"RLlibMLPModel initialized with {self.model.get_num_parameters():,} parameters")
        
    @override(ModelV2)
    def forward(
        self,
        input_dict: Dict[str, TensorType],
        state: List[TensorType],
        seq_lens: TensorType,
    ) -> Tuple[TensorType, List[TensorType]]:
        """
        Forward pass through the model.

        Args:
            input_dict: Input dictionary containing observations
            state: RNN state (not used for MLP)
            seq_lens: Sequence lengths (not used)

        Returns:
            Tuple of (action_logits, new_state)
        """
        obs = input_dict["obs"]

        # Ensure input tensor is on the same device as the model
        if obs.device != next(self.model.parameters()).device:
            obs = obs.to(next(self.model.parameters()).device)

        # Forward pass through MLP
        action_logits, value = self.model(obs)

        # Store value for value_function method
        self._value_out = value

        return action_logits, state
        
    @override(ModelV2)
    def value_function(self) -> TensorType:
        """
        Return the value function output.
        
        Returns:
            Value function output
        """
        if self._value_out is None:
            return torch.zeros(1)
        return self._value_out


class RLlibLSTMModel(TorchModelV2, nn.Module):
    """RLlib adapter for LSTM model."""
    
    def __init__(
        self,
        obs_space,
        action_space,
        num_outputs: int,
        model_config: Dict[str, Any],
        name: str,
        **kwargs
    ):
        """
        Initialize RLlib LSTM model.
        
        Args:
            obs_space: Observation space
            action_space: Action space
            num_outputs: Number of action outputs
            model_config: Model configuration
            name: Model name
            **kwargs: Additional arguments
        """
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        # Extract configuration
        custom_config = model_config.get("custom_model_config", {})
        
        # Model parameters
        self.input_size = int(np.prod(obs_space.shape))
        self.hidden_size = custom_config.get('hidden_size', 256)
        self.num_layers = custom_config.get('num_layers', 2)
        self.dropout = custom_config.get('dropout', 0.1)
        self.bidirectional = custom_config.get('bidirectional', False)
        
        # LSTM layer
        self.lstm = nn.LSTM(
            input_size=self.input_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            dropout=self.dropout if self.num_layers > 1 else 0,
            bidirectional=self.bidirectional,
            batch_first=True
        )
        
        # Calculate LSTM output size
        lstm_output_size = self.hidden_size * (2 if self.bidirectional else 1)
        
        # Output heads
        self.policy_head = nn.Sequential(
            nn.Linear(lstm_output_size, self.hidden_size),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size, num_outputs)
        )
        
        self.value_head = nn.Sequential(
            nn.Linear(lstm_output_size, self.hidden_size),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size, 1)
        )
        
        # Value function output
        self._value_out = None
        
        logger.info(f"RLlibLSTMModel initialized")
        
    @override(ModelV2)
    def forward(
        self,
        input_dict: Dict[str, TensorType],
        state: List[TensorType],
        seq_lens: TensorType,
    ) -> Tuple[TensorType, List[TensorType]]:
        """
        Forward pass through the model.
        
        Args:
            input_dict: Input dictionary containing observations
            state: LSTM state
            seq_lens: Sequence lengths
            
        Returns:
            Tuple of (action_logits, new_state)
        """
        obs = input_dict["obs"]
        batch_size = obs.size(0)
        
        # Reshape for LSTM (add sequence dimension)
        obs = obs.unsqueeze(1)  # [batch_size, 1, input_size]
        
        # Initialize hidden state if not provided
        if not state:
            h_0 = torch.zeros(
                self.num_layers * (2 if self.bidirectional else 1),
                batch_size,
                self.hidden_size,
                device=obs.device
            )
            c_0 = torch.zeros(
                self.num_layers * (2 if self.bidirectional else 1),
                batch_size,
                self.hidden_size,
                device=obs.device
            )
            state = [h_0, c_0]
            
        # LSTM forward pass
        lstm_out, new_state = self.lstm(obs, (state[0], state[1]))
        
        # Use last timestep output
        lstm_out = lstm_out[:, -1, :]  # [batch_size, lstm_output_size]
        
        # Get policy and value outputs
        action_logits = self.policy_head(lstm_out)
        value = self.value_head(lstm_out).squeeze(-1)
        
        # Store value for value_function method
        self._value_out = value
        
        return action_logits, [new_state[0], new_state[1]]
        
    @override(ModelV2)
    def value_function(self) -> TensorType:
        """
        Return the value function output.
        
        Returns:
            Value function output
        """
        if self._value_out is None:
            return torch.zeros(1)
        return self._value_out
        
    @override(ModelV2)
    def get_initial_state(self) -> List[TensorType]:
        """
        Get initial LSTM state.
        
        Returns:
            Initial state list
        """
        return [
            torch.zeros(self.num_layers * (2 if self.bidirectional else 1), self.hidden_size),
            torch.zeros(self.num_layers * (2 if self.bidirectional else 1), self.hidden_size)
        ]
