"""
Multi-Layer Perceptron (MLP) model implementation.
"""

from typing import List, <PERSON>ple
import torch
import torch.nn as nn
from dataclasses import dataclass

from rl_ct.models.base_model import BaseModel, ModelConfig, get_activation_function, initialize_weights
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MLPConfig(ModelConfig):
    """Configuration for MLP model."""
    
    # MLP specific parameters
    hidden_layers: List[int] = None
    use_batch_norm: bool = False
    use_layer_norm: bool = True
    
    def __post_init__(self):
        super().__post_init__()
        if self.hidden_layers is None:
            self.hidden_layers = [self.hidden_size, self.hidden_size]


class MLPBlock(nn.Module):
    """MLP block with normalization and dropout."""
    
    def __init__(
        self,
        input_size: int,
        output_size: int,
        activation: str = "relu",
        dropout: float = 0.1,
        use_batch_norm: bool = False,
        use_layer_norm: bool = True
    ):
        """
        Initialize MLP block.
        
        Args:
            input_size: Input dimension
            output_size: Output dimension
            activation: Activation function
            dropout: Dropout rate
            use_batch_norm: Whether to use batch normalization
            use_layer_norm: Whether to use layer normalization
        """
        super().__init__()
        
        self.linear = nn.Linear(input_size, output_size)
        self.activation = get_activation_function(activation)
        self.dropout = nn.Dropout(dropout)
        
        # Normalization
        if use_batch_norm:
            self.norm = nn.BatchNorm1d(output_size)
        elif use_layer_norm:
            self.norm = nn.LayerNorm(output_size)
        else:
            self.norm = nn.Identity()
            
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        x = self.linear(x)
        x = self.norm(x)
        x = self.activation(x)
        x = self.dropout(x)
        return x


class MLPModel(BaseModel):
    """Multi-Layer Perceptron model for cryptocurrency trading."""
    
    def __init__(self, config: MLPConfig):
        """
        Initialize MLP model.
        
        Args:
            config: MLP configuration
        """
        super().__init__(config)
        self.config = config
        
        # Build encoder layers
        layers = []
        input_size = config.input_size
        
        for hidden_size in config.hidden_layers:
            layers.append(
                MLPBlock(
                    input_size=input_size,
                    output_size=hidden_size,
                    activation=config.activation,
                    dropout=config.dropout,
                    use_batch_norm=config.use_batch_norm,
                    use_layer_norm=config.use_layer_norm
                )
            )
            input_size = hidden_size
            
        self.encoder = nn.Sequential(*layers)
        
        # Output heads
        final_hidden_size = config.hidden_layers[-1] if config.hidden_layers else config.input_size
        
        self.policy_head = nn.Sequential(
            nn.Linear(final_hidden_size, config.hidden_size),
            get_activation_function(config.activation),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_size, config.output_size)
        )
        
        self.value_head = nn.Sequential(
            nn.Linear(final_hidden_size, config.hidden_size),
            get_activation_function(config.activation),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_size, 1)
        )
        
        # Initialize weights
        self.apply(lambda m: initialize_weights(m, "xavier_uniform"))
        
        # Move to device
        self.to_device()
        
        logger.info(f"MLPModel initialized with {self.get_num_parameters():,} parameters")
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the model.
        
        Args:
            x: Input tensor [batch_size, input_size]
            
        Returns:
            Tuple of (policy_logits, value)
        """
        # Encode input
        encoded = self.encoder(x)
        
        # Get policy and value outputs
        policy_logits = self.policy_head(encoded)
        value = self.value_head(encoded).squeeze(-1)
        
        return policy_logits, value


class ResidualMLPBlock(nn.Module):
    """MLP block with residual connection."""
    
    def __init__(
        self,
        hidden_size: int,
        activation: str = "relu",
        dropout: float = 0.1,
        use_layer_norm: bool = True
    ):
        """
        Initialize residual MLP block.
        
        Args:
            hidden_size: Hidden dimension
            activation: Activation function
            dropout: Dropout rate
            use_layer_norm: Whether to use layer normalization
        """
        super().__init__()
        
        self.linear1 = nn.Linear(hidden_size, hidden_size)
        self.linear2 = nn.Linear(hidden_size, hidden_size)
        self.activation = get_activation_function(activation)
        self.dropout = nn.Dropout(dropout)
        
        if use_layer_norm:
            self.norm1 = nn.LayerNorm(hidden_size)
            self.norm2 = nn.LayerNorm(hidden_size)
        else:
            self.norm1 = nn.Identity()
            self.norm2 = nn.Identity()
            
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass with residual connection."""
        residual = x
        
        x = self.linear1(x)
        x = self.norm1(x)
        x = self.activation(x)
        x = self.dropout(x)
        
        x = self.linear2(x)
        x = self.norm2(x)
        
        # Residual connection
        x = x + residual
        x = self.activation(x)
        
        return x


class ResidualMLPModel(BaseModel):
    """Residual MLP model with skip connections."""
    
    def __init__(self, config: MLPConfig):
        """
        Initialize residual MLP model.
        
        Args:
            config: MLP configuration
        """
        super().__init__(config)
        self.config = config
        
        # Input projection
        self.input_projection = nn.Linear(config.input_size, config.hidden_size)
        
        # Residual blocks
        self.residual_blocks = nn.ModuleList([
            ResidualMLPBlock(
                hidden_size=config.hidden_size,
                activation=config.activation,
                dropout=config.dropout,
                use_layer_norm=config.use_layer_norm
            )
            for _ in range(config.num_layers)
        ])
        
        # Output heads
        self.policy_head = nn.Sequential(
            nn.Linear(config.hidden_size, config.hidden_size),
            get_activation_function(config.activation),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_size, config.output_size)
        )
        
        self.value_head = nn.Sequential(
            nn.Linear(config.hidden_size, config.hidden_size),
            get_activation_function(config.activation),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_size, 1)
        )
        
        # Initialize weights
        self.apply(lambda m: initialize_weights(m, "xavier_uniform"))
        
        # Move to device
        self.to_device()
        
        logger.info(f"ResidualMLPModel initialized with {self.get_num_parameters():,} parameters")
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the model.
        
        Args:
            x: Input tensor [batch_size, input_size]
            
        Returns:
            Tuple of (policy_logits, value)
        """
        # Project input to hidden size
        x = self.input_projection(x)
        x = get_activation_function(self.config.activation)(x)
        
        # Pass through residual blocks
        for block in self.residual_blocks:
            x = block(x)
            
        # Get policy and value outputs
        policy_logits = self.policy_head(x)
        value = self.value_head(x).squeeze(-1)
        
        return policy_logits, value


def create_model(model_type: str, config: MLPConfig) -> BaseModel:
    """
    Create model based on type.
    
    Args:
        model_type: Type of model ('mlp', 'residual_mlp')
        config: Model configuration
        
    Returns:
        Created model
    """
    if model_type == "mlp":
        return MLPModel(config)
    elif model_type == "residual_mlp":
        return ResidualMLPModel(config)
    else:
        raise ValueError(f"Unknown model type: {model_type}")
