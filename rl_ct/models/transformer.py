"""
Transformer model implementation for cryptocurrency trading.
"""

import math
from typing import Optional, <PERSON><PERSON>
import torch
import torch.nn as nn
from dataclasses import dataclass

from rl_ct.models.base_model import BaseModel, ModelConfig, get_activation_function, initialize_weights
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class InputSplit(nn.Module):
    """Split flattened observation history into logical segments."""

    def __init__(self, config):
        """
        Initialize input split module.

        Args:
            config: Transformer configuration
        """
        super().__init__()
        self.config = config
        self.num_obs_in_history = config.num_obs_in_history
        self.d_obs = config.d_obs
        self.d_obs_logical_segments = config.d_obs_logical_segments

    def forward(self, obs_history_flat: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Split flattened observation history.

        Args:
            obs_history_flat: Flattened observation history [batch_size, d_history_flat]

        Returns:
            Tuple of (time_data, account_data, technical_data)
        """
        batch_size = obs_history_flat.size(0)

        # Reshape to [batch_size, seq_len, d_obs]
        obs_history = obs_history_flat.view(batch_size, self.num_obs_in_history, self.d_obs)

        # Split along the feature dimension
        obs_logical_segments = torch.split(
            obs_history,
            self.d_obs_logical_segments,
            dim=-1
        )

        obs_history_time = obs_logical_segments[0]  # [batch_size, seq_len, d_time]
        obs_history_account = obs_logical_segments[1]  # [batch_size, seq_len, d_account]
        obs_history_technical = obs_logical_segments[2]  # [batch_size, seq_len, d_technical]

        return obs_history_time, obs_history_account, obs_history_technical


class TimeEncoding(nn.Module):
    """Encode time information including absolute time and calendar features."""

    def __init__(self, num_obs_in_history: int = 168, d_time_enc: int = 32):
        """
        Initialize time encoding module.

        Args:
            num_obs_in_history: Number of observations in history
            d_time_enc: Time encoding dimension
        """
        super().__init__()
        self.num_obs_in_history = num_obs_in_history
        self.d_time_enc = d_time_enc

        # Dense layer for time encoding
        self.dense_time_encoding = nn.Linear(3, d_time_enc)  # 1 (abs time) + 2 (calendar features)
        self.layer_norm_time = nn.LayerNorm(d_time_enc)

    def forward(self, obs_history_time: torch.Tensor) -> torch.Tensor:
        """
        Encode time information.

        Args:
            obs_history_time: Time observations [batch_size, seq_len, d_time]

        Returns:
            Encoded time features [batch_size, seq_len, d_time_enc]
        """
        batch_size, seq_len, _ = obs_history_time.shape

        # Create absolute time scaled from 0 to 1 across the sequence
        time_abs_scaled = torch.linspace(0.0, 1.0, self.num_obs_in_history, device=obs_history_time.device)
        time_abs_scaled = time_abs_scaled.unsqueeze(0).unsqueeze(-1).expand(batch_size, seq_len, 1)

        # Concatenate absolute time with calendar/clock features
        time_concat = torch.cat([time_abs_scaled, obs_history_time], dim=-1)

        # Apply dense encoding
        obs_history_time_enc = self.dense_time_encoding(time_concat)
        obs_history_time_enc = self.layer_norm_time(obs_history_time_enc)

        return obs_history_time_enc


class AccountEncoding(nn.Module):
    """Encode account state information."""

    def __init__(self, d_account_enc: int = 32):
        """
        Initialize account encoding module.

        Args:
            d_account_enc: Account encoding dimension
        """
        super().__init__()
        self.d_account_enc = d_account_enc

        # Dense layer for account encoding
        self.dense_account_encoding = nn.Linear(2, d_account_enc)  # Assuming 2D account info
        self.layer_norm_account = nn.LayerNorm(d_account_enc)

    def forward(self, obs_history_account: torch.Tensor) -> torch.Tensor:
        """
        Encode account information.

        Args:
            obs_history_account: Account observations [batch_size, seq_len, d_account]

        Returns:
            Encoded account features [batch_size, seq_len, d_account_enc]
        """
        obs_history_account_enc = self.dense_account_encoding(obs_history_account)
        obs_history_account_enc = self.layer_norm_account(obs_history_account_enc)

        return obs_history_account_enc


class TechnicalIndicatorEncoding(nn.Module):
    """Encode technical indicator data."""

    def __init__(self, d_technical_indicators: int = 96, d_technical_enc: int = 192):
        """
        Initialize technical indicator encoding module.

        Args:
            d_technical_indicators: Technical indicators input dimension
            d_technical_enc: Technical indicators encoding dimension
        """
        super().__init__()
        self.d_technical_indicators = d_technical_indicators
        self.d_technical_enc = d_technical_enc

        # Intermediate encoding dimension
        self.d_technical_enc_intermediate = d_technical_enc * 4 // 3

        # Dense layers for technical indicator encoding
        self.dense_technical_intermediate = nn.Linear(
            d_technical_indicators,
            self.d_technical_enc_intermediate
        )
        self.dense_technical_encoding = nn.Linear(
            self.d_technical_enc_intermediate,
            d_technical_enc
        )
        self.layer_norm_technical = nn.LayerNorm(d_technical_enc)
        self.activation = nn.GELU()

    def forward(self, obs_history_technical: torch.Tensor) -> torch.Tensor:
        """
        Encode technical indicator information.

        Args:
            obs_history_technical: Technical indicator observations [batch_size, seq_len, d_technical]

        Returns:
            Encoded technical features [batch_size, seq_len, d_technical_enc]
        """
        # Apply intermediate encoding with activation
        obs_history_technical_intermediate = self.dense_technical_intermediate(obs_history_technical)
        obs_history_technical_intermediate = self.activation(obs_history_technical_intermediate)

        # Apply final encoding
        obs_history_technical_enc = self.dense_technical_encoding(obs_history_technical_intermediate)
        obs_history_technical_enc = self.layer_norm_technical(obs_history_technical_enc)

        return obs_history_technical_enc


class ObsEncodingInternal(nn.Module):
    """Encode internal observations (time + account)."""

    def __init__(self, num_obs_in_history: int = 168, d_time_enc: int = 32, d_account_enc: int = 32):
        """
        Initialize internal observation encoding.

        Args:
            num_obs_in_history: Number of observations in history
            d_time_enc: Time encoding dimension
            d_account_enc: Account encoding dimension
        """
        super().__init__()
        self.time_encoding = TimeEncoding(num_obs_in_history, d_time_enc)
        self.account_encoding = AccountEncoding(d_account_enc)

    def forward(self, obs_history_time: torch.Tensor, obs_history_account: torch.Tensor) -> torch.Tensor:
        """
        Encode internal observations.

        Args:
            obs_history_time: Time observations [batch_size, seq_len, d_time]
            obs_history_account: Account observations [batch_size, seq_len, d_account]

        Returns:
            Internal encoded features [batch_size, seq_len, d_time_enc + d_account_enc]
        """
        obs_history_time_enc = self.time_encoding(obs_history_time)
        obs_history_account_enc = self.account_encoding(obs_history_account)

        # Concatenate time and account encodings
        obs_history_internal_enc = torch.cat([obs_history_time_enc, obs_history_account_enc], dim=-1)

        return obs_history_internal_enc


class ObsEncodingExternal(nn.Module):
    """Encode external observations (technical indicators)."""

    def __init__(self, d_technical_indicators: int = 96, d_technical_enc: int = 192):
        """
        Initialize external observation encoding.

        Args:
            d_technical_indicators: Technical indicators input dimension
            d_technical_enc: Technical indicators encoding dimension
        """
        super().__init__()
        self.technical_encoding = TechnicalIndicatorEncoding(d_technical_indicators, d_technical_enc)

    def forward(self, obs_history_technical: torch.Tensor) -> torch.Tensor:
        """
        Encode external observations.

        Args:
            obs_history_technical: Technical indicator observations [batch_size, seq_len, d_technical]

        Returns:
            External encoded features [batch_size, seq_len, d_technical_enc]
        """
        obs_history_external_enc = self.technical_encoding(obs_history_technical)

        return obs_history_external_enc


class Stem(nn.Module):
    """Stem module that combines internal and external encodings."""

    def __init__(self, config):
        """
        Initialize stem module.

        Args:
            config: Transformer configuration
        """
        super().__init__()
        self.config = config

        self.obs_encoding_internal = ObsEncodingInternal(
            num_obs_in_history=config.num_obs_in_history,
            d_time_enc=config.d_time_enc,
            d_account_enc=config.d_account_enc
        )

        self.obs_encoding_external = ObsEncodingExternal(
            d_technical_indicators=config.d_technical_indicators,
            d_technical_enc=config.d_technical_enc
        )

    def forward(
        self,
        obs_history_time: torch.Tensor,
        obs_history_account: torch.Tensor,
        obs_history_technical: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Process observations through stem.

        Args:
            obs_history_time: Time observations
            obs_history_account: Account observations
            obs_history_technical: Technical indicator observations

        Returns:
            Tuple of (internal_encoding, external_encoding)
        """
        obs_history_internal_enc = self.obs_encoding_internal(obs_history_time, obs_history_account)
        obs_history_external_enc = self.obs_encoding_external(obs_history_technical)

        return obs_history_internal_enc, obs_history_external_enc


class AttentionBlock(nn.Module):
    """Attention block that processes internal and external encodings separately."""

    def __init__(
        self,
        num_heads: int = 4,
        dropout_rate: float = 0.1,
        d_obs_internal_enc: int = 64,
        d_obs_external_enc: int = 192
    ):
        """
        Initialize attention block.

        Args:
            num_heads: Number of attention heads
            dropout_rate: Dropout rate
            d_obs_internal_enc: Internal encoding dimension
            d_obs_external_enc: External encoding dimension
        """
        super().__init__()
        self.num_heads = num_heads
        self.dropout_rate = dropout_rate
        self.d_obs_internal_enc = d_obs_internal_enc
        self.d_obs_external_enc = d_obs_external_enc
        self.d_model = d_obs_internal_enc + d_obs_external_enc
        self.d_key = self.d_model // num_heads
        self.d_ff_intermediate = self.d_model * 3 // 2

        # Multi-head attention
        self.attn_mha = nn.MultiheadAttention(
            embed_dim=self.d_model,
            num_heads=num_heads,
            dropout=dropout_rate,
            batch_first=True
        )

        # Attention projection and normalization
        self.attn_dense = nn.Linear(self.d_model, d_obs_external_enc)
        self.attn_layer_norm = nn.LayerNorm(d_obs_external_enc)

        # Feed-forward network
        self.ff_dense_1 = nn.Linear(self.d_model, self.d_ff_intermediate)
        self.ff_dense_2 = nn.Linear(self.d_ff_intermediate, d_obs_external_enc)
        self.ff_dropout = nn.Dropout(dropout_rate)
        self.ff_layer_norm = nn.LayerNorm(d_obs_external_enc)
        self.activation = nn.GELU()

    def forward(
        self,
        obs_history_internal_enc: torch.Tensor,
        obs_history_external_enc: torch.Tensor
    ) -> torch.Tensor:
        """
        Forward pass through attention block.

        Args:
            obs_history_internal_enc: Internal encoded features [batch_size, seq_len, d_internal]
            obs_history_external_enc: External encoded features [batch_size, seq_len, d_external]

        Returns:
            Updated external encoded features [batch_size, seq_len, d_external]
        """
        # Store input for residual connection
        attn_block_input = obs_history_external_enc

        # Concatenate internal and external encodings for attention
        obs_history_enc = torch.cat([obs_history_internal_enc, attn_block_input], dim=-1)

        # Multi-head attention (self-attention)
        attn_mha_output, _ = self.attn_mha(obs_history_enc, obs_history_enc, obs_history_enc)

        # Project attention output back to external dimension
        attn_dense_output = self.attn_dense(attn_mha_output)

        # Residual connection and layer norm
        attn_add_output = attn_block_input + attn_dense_output
        attn_block_output = self.attn_layer_norm(attn_add_output)

        # Feed-forward block
        ff_block_input = attn_block_output

        # Concatenate again for feed-forward
        obs_history_enc = torch.cat([obs_history_internal_enc, ff_block_input], dim=-1)

        # Feed-forward layers
        ff_dense_1_output = self.ff_dense_1(obs_history_enc)
        ff_dense_1_output = self.activation(ff_dense_1_output)
        ff_dense_2_output = self.ff_dense_2(ff_dense_1_output)
        ff_dropout_output = self.ff_dropout(ff_dense_2_output)

        # Residual connection and layer norm
        ff_add_output = ff_block_input + ff_dropout_output
        ff_block_output = self.ff_layer_norm(ff_add_output)

        return ff_block_output


class TransformerOutputTimePooling(nn.Module):
    """Time pooling module to extract final representation from sequence."""

    def __init__(self):
        """Initialize time pooling module."""
        super().__init__()

    def forward(
        self,
        obs_history_stem_enc: torch.Tensor,
        obs_history_transformer_enc: torch.Tensor
    ) -> torch.Tensor:
        """
        Pool time dimension to get final representation.

        Args:
            obs_history_stem_enc: Stem encoded features [batch_size, seq_len, d_stem]
            obs_history_transformer_enc: Transformer encoded features [batch_size, seq_len, d_external]

        Returns:
            Final pooled representation [batch_size, d_stem + d_external]
        """
        # Take the last timestep from both encodings
        last_obs_stem_enc = obs_history_stem_enc[:, -1, :]  # [batch_size, d_stem]
        last_obs_transformer_enc = obs_history_transformer_enc[:, -1, :]  # [batch_size, d_external]

        # Concatenate the final representations
        last_obs_enc = torch.cat([last_obs_stem_enc, last_obs_transformer_enc], dim=-1)

        return last_obs_enc


class ActionBranch(nn.Module):
    """Action branch for policy output."""

    def __init__(self, input_dim: int, num_outputs: int):
        """
        Initialize action branch.

        Args:
            input_dim: Input dimension
            num_outputs: Number of action outputs
        """
        super().__init__()
        self.dense = nn.Linear(input_dim, num_outputs)

    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through action branch.

        Args:
            inputs: Input tensor [batch_size, input_dim]

        Returns:
            Action logits [batch_size, num_outputs]
        """
        return self.dense(inputs)


class ValueBranch(nn.Module):
    """Value branch for value function output."""

    def __init__(self, input_dim: int):
        """
        Initialize value branch.

        Args:
            input_dim: Input dimension
        """
        super().__init__()
        self.dense = nn.Linear(input_dim, 1)

    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through value branch.

        Args:
            inputs: Input tensor [batch_size, input_dim]

        Returns:
            Value estimates [batch_size, 1]
        """
        return self.dense(inputs)


@dataclass
class TransformerConfig(ModelConfig):
    """Configuration for Transformer model."""

    # Data dimensions
    num_obs_in_history: int = 168  # Sequence length
    d_obs: int = 100  # Total observation dimension per timestep

    # Logical data segments dimensions
    d_time: int = 2  # Time information dimension
    d_account: int = 2  # Account information dimension
    d_technical_indicators: int = 96  # Technical indicators dimension (d_obs - d_time - d_account)

    # Encoding dimensions
    d_obs_enc: int = 256  # Total encoding dimension
    d_time_enc: int = 32  # Time encoding dimension (d_obs_enc // 8)
    d_account_enc: int = 32  # Account encoding dimension (d_obs_enc // 8)
    d_technical_enc: int = 192  # Technical indicators encoding dimension (d_obs_enc * 3 // 4)

    # Transformer architecture
    d_model: int = 256  # Should equal d_obs_enc
    num_heads: int = 4
    d_ff: int = 384  # Feed-forward dimension (d_model * 3 // 2)
    num_attn_blocks: int = 3  # Number of attention blocks
    max_seq_length: int = 168
    use_positional_encoding: bool = False  # Reference implementation doesn't use it

    # Architecture
    num_encoder_layers: int = 0  # Not used in new architecture
    num_decoder_layers: int = 0  # Not used in new architecture

    def __post_init__(self):
        super().__post_init__()

        # Validate dimensions
        assert self.d_obs_enc % 8 == 0, "d_obs_enc must be divisible by 8"
        assert self.d_obs == (self.d_time + self.d_account + self.d_technical_indicators), \
            "d_obs must equal sum of logical segments"
        assert self.d_model == self.d_obs_enc, "d_model must equal d_obs_enc"

        # Calculate derived dimensions
        self.d_obs_internal_enc = self.d_time_enc + self.d_account_enc
        self.d_obs_external_enc = self.d_technical_enc
        self.d_obs_stem_enc = self.d_obs_internal_enc + self.d_obs_external_enc
        self.d_history_flat = self.num_obs_in_history * self.d_obs

        # Set input size for compatibility
        self.input_size = self.d_history_flat
        self.feature_dim = self.d_obs  # For compatibility
        self.lookback_window = self.num_obs_in_history  # For compatibility

        # Logical segments for input splitting
        self.d_obs_logical_segments = [
            self.d_time,
            self.d_account,
            self.d_technical_indicators
        ]


class NewTransformerModel(BaseModel):
    """New Transformer model for cryptocurrency trading based on reference implementation."""

    def __init__(self, config):
        """
        Initialize new transformer model.

        Args:
            config: Transformer configuration
        """
        super().__init__(config)
        self.config = config

        # Input splitting
        self.input_split = InputSplit(config)

        # Stem module
        self.stem = Stem(config)

        # Attention blocks
        self.attention_blocks = nn.ModuleList([
            AttentionBlock(
                num_heads=config.num_heads,
                dropout_rate=config.dropout,
                d_obs_internal_enc=config.d_obs_internal_enc,
                d_obs_external_enc=config.d_obs_external_enc
            )
            for _ in range(config.num_attn_blocks)
        ])

        # Output pooling
        self.transformer_output_time_pooling = TransformerOutputTimePooling()

        # Output branches
        final_dim = config.d_obs_stem_enc + config.d_obs_external_enc
        self.action_branch = ActionBranch(final_dim, config.output_size)
        self.value_branch = ValueBranch(final_dim)

        # Initialize weights
        self.apply(lambda m: initialize_weights(m, "xavier_uniform"))

        # Move to device
        self.to_device()

        logger.info(f"NewTransformerModel initialized with {self.get_num_parameters():,} parameters")

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the new transformer model.

        Args:
            x: Input tensor [batch_size, d_history_flat]

        Returns:
            Tuple of (policy_logits, value)
        """
        # Split input into logical segments
        obs_history_time, obs_history_account, obs_history_technical = self.input_split(x)

        # Process through stem
        obs_history_internal_enc, obs_history_external_enc = self.stem(
            obs_history_time, obs_history_account, obs_history_technical
        )

        # Concatenate for stem encoding
        obs_history_stem_enc = torch.cat([obs_history_internal_enc, obs_history_external_enc], dim=-1)

        # Process through attention blocks
        for attention_block in self.attention_blocks:
            obs_history_external_enc = attention_block(obs_history_internal_enc, obs_history_external_enc)

        # Final transformer encoding
        obs_history_transformer_enc = obs_history_external_enc

        # Time pooling to get final representation
        current_obs_transformer_enc = self.transformer_output_time_pooling(
            obs_history_stem_enc, obs_history_transformer_enc
        )

        # Get policy and value outputs
        policy_logits = self.action_branch(current_obs_transformer_enc)
        value = self.value_branch(current_obs_transformer_enc).squeeze(-1)

        return policy_logits, value


# Alias for backward compatibility
TransformerModel = NewTransformerModel
