"""
Transformer model implementation for cryptocurrency trading.
"""

import math
from typing import Optional, <PERSON>ple
import torch
import torch.nn as nn
from dataclasses import dataclass

from rl_ct.models.base_model import BaseModel, ModelConfig, get_activation_function, initialize_weights
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TransformerConfig(ModelConfig):
    """Configuration for Transformer model."""
    
    # Transformer specific parameters
    d_model: int = 256
    num_heads: int = 8
    d_ff: int = 512
    max_seq_length: int = 168
    use_positional_encoding: bool = True
    
    # Input processing
    lookback_window: int = 168
    feature_dim: int = 100
    
    # Architecture
    num_encoder_layers: int = 3
    num_decoder_layers: int = 0  # 0 for encoder-only
    
    def __post_init__(self):
        super().__post_init__()
        # Set input size based on sequence and features
        self.input_size = self.lookback_window * self.feature_dim


class PositionalEncoding(nn.Module):
    """Positional encoding for transformer."""
    
    def __init__(self, d_model: int, max_seq_length: int = 5000):
        """
        Initialize positional encoding.
        
        Args:
            d_model: Model dimension
            max_seq_length: Maximum sequence length
        """
        super().__init__()
        
        pe = torch.zeros(max_seq_length, d_model)
        position = torch.arange(0, max_seq_length, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Add positional encoding to input.
        
        Args:
            x: Input tensor [seq_len, batch_size, d_model]
            
        Returns:
            Tensor with positional encoding added
        """
        return x + self.pe[:x.size(0), :]


class MultiHeadAttention(nn.Module):
    """Multi-head attention mechanism."""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        """
        Initialize multi-head attention.
        
        Args:
            d_model: Model dimension
            num_heads: Number of attention heads
            dropout: Dropout rate
        """
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)
        
    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Forward pass of multi-head attention.
        
        Args:
            query: Query tensor
            key: Key tensor
            value: Value tensor
            mask: Optional attention mask
            
        Returns:
            Attention output
        """
        batch_size, seq_len, d_model = query.size()
        
        # Linear projections
        Q = self.w_q(query).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        # Attention
        attention_output = self._attention(Q, K, V, mask)
        
        # Concatenate heads
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, d_model
        )
        
        # Final linear projection
        output = self.w_o(attention_output)
        
        return output
        
    def _attention(
        self,
        Q: torch.Tensor,
        K: torch.Tensor,
        V: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """Compute scaled dot-product attention."""
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
            
        attention_weights = torch.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        output = torch.matmul(attention_weights, V)
        return output


class FeedForward(nn.Module):
    """Feed-forward network."""
    
    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1, activation: str = "gelu"):
        """
        Initialize feed-forward network.
        
        Args:
            d_model: Model dimension
            d_ff: Feed-forward dimension
            dropout: Dropout rate
            activation: Activation function
        """
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        self.activation = get_activation_function(activation)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        return self.linear2(self.dropout(self.activation(self.linear1(x))))


class TransformerEncoderLayer(nn.Module):
    """Transformer encoder layer."""
    
    def __init__(
        self,
        d_model: int,
        num_heads: int,
        d_ff: int,
        dropout: float = 0.1,
        activation: str = "gelu"
    ):
        """
        Initialize transformer encoder layer.
        
        Args:
            d_model: Model dimension
            num_heads: Number of attention heads
            d_ff: Feed-forward dimension
            dropout: Dropout rate
            activation: Activation function
        """
        super().__init__()
        self.self_attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout, activation)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass."""
        # Self-attention with residual connection
        attn_output = self.self_attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Feed-forward with residual connection
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x


class TransformerEncoder(nn.Module):
    """Transformer encoder."""
    
    def __init__(self, config: TransformerConfig):
        """
        Initialize transformer encoder.
        
        Args:
            config: Transformer configuration
        """
        super().__init__()
        self.config = config
        
        # Input projection
        self.input_projection = nn.Linear(config.feature_dim, config.d_model)
        
        # Positional encoding
        if config.use_positional_encoding:
            self.positional_encoding = PositionalEncoding(config.d_model, config.max_seq_length)
        else:
            self.positional_encoding = None
            
        # Encoder layers
        self.layers = nn.ModuleList([
            TransformerEncoderLayer(
                d_model=config.d_model,
                num_heads=config.num_heads,
                d_ff=config.d_ff,
                dropout=config.dropout,
                activation=config.activation
            )
            for _ in range(config.num_encoder_layers)
        ])
        
        self.dropout = nn.Dropout(config.dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass through encoder.
        
        Args:
            x: Input tensor [batch_size, seq_len, feature_dim]
            mask: Optional attention mask
            
        Returns:
            Encoded tensor [batch_size, seq_len, d_model]
        """
        # Input projection
        x = self.input_projection(x)
        
        # Add positional encoding
        if self.positional_encoding is not None:
            x = x.transpose(0, 1)  # [seq_len, batch_size, d_model]
            x = self.positional_encoding(x)
            x = x.transpose(0, 1)  # [batch_size, seq_len, d_model]
            
        x = self.dropout(x)
        
        # Pass through encoder layers
        for layer in self.layers:
            x = layer(x, mask)
            
        return x


class TransformerModel(BaseModel):
    """Transformer model for cryptocurrency trading."""
    
    def __init__(self, config: TransformerConfig):
        """
        Initialize transformer model.
        
        Args:
            config: Transformer configuration
        """
        super().__init__(config)
        self.config = config
        
        # Encoder
        self.encoder = TransformerEncoder(config)
        
        # Output heads
        self.policy_head = nn.Sequential(
            nn.Linear(config.d_model, config.hidden_size),
            get_activation_function(config.activation),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_size, config.output_size)
        )
        
        self.value_head = nn.Sequential(
            nn.Linear(config.d_model, config.hidden_size),
            get_activation_function(config.activation),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_size, 1)
        )
        
        # Initialize weights
        self.apply(lambda m: initialize_weights(m, "xavier_uniform"))
        
        # Move to device
        self.to_device()
        
        logger.info(f"TransformerModel initialized with {self.get_num_parameters():,} parameters")
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the model.
        
        Args:
            x: Input tensor [batch_size, seq_len * feature_dim]
            
        Returns:
            Tuple of (policy_logits, value)
        """
        batch_size = x.size(0)
        
        # Reshape input to [batch_size, seq_len, feature_dim]
        x = x.view(batch_size, self.config.lookback_window, self.config.feature_dim)
        
        # Encode sequence
        encoded = self.encoder(x)
        
        # Use last timestep for prediction
        last_hidden = encoded[:, -1, :]  # [batch_size, d_model]
        
        # Get policy and value outputs
        policy_logits = self.policy_head(last_hidden)
        value = self.value_head(last_hidden).squeeze(-1)
        
        return policy_logits, value
