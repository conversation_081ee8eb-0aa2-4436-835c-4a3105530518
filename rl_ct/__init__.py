"""
RL-CT: Reinforcement Learning for Cryptocurrency Trading

A PyTorch and Ray based framework for training and evaluating 
reinforcement learning agents on cryptocurrency trading tasks.
"""

__version__ = "0.1.0"
__author__ = "RL-CT Team"
__email__ = "<EMAIL>"

from rl_ct.envs import CryptoTradingEnv
from rl_ct.models import TransformerModel
from rl_ct.agents import PPOAgent

__all__ = [
    "CryptoTradingEnv",
    "TransformerModel", 
    "PPOAgent",
]
