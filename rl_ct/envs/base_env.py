"""
Base trading environment interface.
"""

from abc import ABC, abstractmethod
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, List
import numpy as np
import gymnasium as gym
from gymnasium import spaces

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class BaseTradingEnv(gym.Env, ABC):
    """Base class for all trading environments."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize base trading environment.
        
        Args:
            config: Environment configuration
        """
        super().__init__()
        self.config = config or {}
        
        # Trading parameters
        self.initial_balance = self.config.get('initial_balance', 10000.0)
        self.transaction_cost = self.config.get('transaction_cost', 0.001)
        self.slippage = self.config.get('slippage', 0.0001)
        
        # Environment state
        self.current_step = 0
        self.max_steps = self.config.get('max_steps', 1000)
        self.balance = self.initial_balance
        self.position = 0.0  # Current position size
        self.position_value = 0.0  # Current position value
        self.entry_price = 0.0  # Entry price of current position
        
        # Data
        self.data: Optional[np.ndarray] = None
        self.prices: Optional[np.ndarray] = None
        
        # Metrics tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_balance = self.initial_balance
        
        # Action and observation spaces (to be defined by subclasses)
        self.action_space: Optional[spaces.Space] = None
        self.observation_space: Optional[spaces.Space] = None
        
    @abstractmethod
    def _get_observation(self) -> np.ndarray:
        """
        Get current observation.
        
        Returns:
            Current observation array
        """
        pass
        
    @abstractmethod
    def _calculate_reward(self, action: int, prev_balance: float) -> float:
        """
        Calculate reward for the current step.
        
        Args:
            action: Action taken
            prev_balance: Previous balance
            
        Returns:
            Reward value
        """
        pass
        
    @abstractmethod
    def _execute_action(self, action: int) -> Dict[str, Any]:
        """
        Execute trading action.
        
        Args:
            action: Action to execute
            
        Returns:
            Dictionary with execution details
        """
        pass
        
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """
        Reset the environment.
        
        Args:
            seed: Random seed
            options: Additional options
            
        Returns:
            Tuple of (observation, info)
        """
        super().reset(seed=seed)
        
        # Reset environment state
        self.current_step = 0
        self.balance = self.initial_balance
        self.position = 0.0
        self.position_value = 0.0
        self.entry_price = 0.0
        
        # Reset metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_balance = self.initial_balance
        
        # Get initial observation
        observation = self._get_observation()
        info = self._get_info()
        
        logger.debug("Environment reset")
        
        return observation, info
        
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """
        Execute one step in the environment.
        
        Args:
            action: Action to take
            
        Returns:
            Tuple of (observation, reward, terminated, truncated, info)
        """
        if self.current_step >= self.max_steps:
            raise RuntimeError("Environment has reached maximum steps")
            
        prev_balance = self.balance
        
        # Execute action
        execution_info = self._execute_action(action)
        
        # Calculate reward
        reward = self._calculate_reward(action, prev_balance)
        
        # Update metrics
        self._update_metrics()
        
        # Move to next step
        self.current_step += 1
        
        # Check if episode is done
        terminated = self._is_terminated()
        truncated = self.current_step >= self.max_steps
        
        # Get new observation
        observation = self._get_observation()
        
        # Prepare info
        info = self._get_info()
        info.update(execution_info)
        
        return observation, reward, terminated, truncated, info
        
    def _is_terminated(self) -> bool:
        """
        Check if episode should be terminated.
        
        Returns:
            True if episode should end
        """
        # Terminate if balance is too low
        if self.balance <= 0:
            return True
            
        # Terminate if maximum drawdown exceeded
        max_dd_threshold = self.config.get('max_drawdown_threshold', 0.5)
        if self.max_drawdown > max_dd_threshold:
            return True
            
        return False
        
    def _update_metrics(self) -> None:
        """Update performance metrics."""
        # Update peak balance and drawdown
        if self.balance > self.peak_balance:
            self.peak_balance = self.balance
            
        current_drawdown = (self.peak_balance - self.balance) / self.peak_balance
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
            
        # Update total PnL
        self.total_pnl = self.balance - self.initial_balance
        
    def _get_info(self) -> Dict[str, Any]:
        """
        Get environment info.
        
        Returns:
            Info dictionary
        """
        return {
            'step': self.current_step,
            'balance': self.balance,
            'position': self.position,
            'position_value': self.position_value,
            'total_pnl': self.total_pnl,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': self.winning_trades / max(self.total_trades, 1),
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self._calculate_sharpe_ratio(),
        }
        
    def _calculate_sharpe_ratio(self) -> float:
        """
        Calculate Sharpe ratio (simplified).
        
        Returns:
            Sharpe ratio
        """
        if self.current_step < 2:
            return 0.0
            
        # This is a simplified calculation
        # In practice, you'd want to track returns over time
        total_return = (self.balance - self.initial_balance) / self.initial_balance
        return total_return / max(self.max_drawdown, 0.01)
        
    def get_current_price(self) -> float:
        """
        Get current market price.
        
        Returns:
            Current price
        """
        if self.prices is None or self.current_step >= len(self.prices):
            return 0.0
        return float(self.prices[self.current_step])
        
    def render(self, mode: str = 'human') -> Optional[np.ndarray]:
        """
        Render the environment.
        
        Args:
            mode: Render mode
            
        Returns:
            Rendered output (if applicable)
        """
        if mode == 'human':
            print(f"Step: {self.current_step}")
            print(f"Balance: ${self.balance:.2f}")
            print(f"Position: {self.position:.4f}")
            print(f"Current Price: ${self.get_current_price():.2f}")
            print(f"Total PnL: ${self.total_pnl:.2f}")
            print(f"Max Drawdown: {self.max_drawdown:.2%}")
            print("-" * 40)
        
        return None
        
    def close(self) -> None:
        """Close the environment."""
        pass
        
    def seed(self, seed: Optional[int] = None) -> List[int]:
        """
        Set random seed.
        
        Args:
            seed: Random seed
            
        Returns:
            List of seeds used
        """
        if seed is not None:
            np.random.seed(seed)
        return [seed] if seed is not None else []
