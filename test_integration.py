#!/usr/bin/env python3
"""
Integration test for the new transformer implementation with environment.
"""

import torch
import numpy as np
from rl_ct.models.transformer import TransformerConfig, NewTransformerModel
from rl_ct.envs.crypto_trading_env import CryptoTradingEnv
from rl_ct.utils.preprocessing.scaler import SegmentedScaler

def test_segmented_scaler():
    """Test the segmented scaler."""
    print("Testing SegmentedScaler...")
    
    # Create test data
    batch_size = 10
    seq_len = 168
    d_time = 2
    d_account = 2
    d_technical = 96
    d_obs = d_time + d_account + d_technical
    
    # Generate test data
    test_data = np.random.randn(batch_size, seq_len, d_obs)
    
    # Create scaler
    scaler = SegmentedScaler(
        d_time=d_time,
        d_account=d_account,
        d_technical=d_technical,
        time_scaler_type="none",
        account_scaler_type="standard",
        technical_scaler_type="minmax"
    )
    
    # Test fit and transform
    scaler.fit(test_data)
    transformed = scaler.transform(test_data)
    inverse_transformed = scaler.inverse_transform(transformed)
    
    print(f"Original data shape: {test_data.shape}")
    print(f"Transformed data shape: {transformed.shape}")
    print(f"Inverse transformed shape: {inverse_transformed.shape}")
    
    # Check that time data is unchanged (no scaler)
    time_original = test_data[:, :, :d_time]
    time_transformed = transformed[:, :, :d_time]
    assert np.allclose(time_original, time_transformed), "Time data should be unchanged"
    
    print("✓ SegmentedScaler working correctly!")
    return scaler

def test_environment_integration():
    """Test environment with new data structure."""
    print("\nTesting environment integration...")
    
    # Create environment config
    env_config = {
        'initial_balance': 10000.0,
        'lookback_window': 168,
        'episode_length': 100,
        'dataset_name': 'test',
        'data_dir': 'data/processed',
        'train_start': [100],
        'train_end': [500],
        'test_start': [500],
        'test_end': [600],
        'regime': 'training'
    }
    
    # Create mock data
    n_samples = 1000
    feature_dim = 96
    price_data = np.random.randn(n_samples, 4)  # OHLC
    feature_data = np.random.randn(n_samples, feature_dim)
    
    # Save mock data
    import os
    os.makedirs('data/processed/test', exist_ok=True)
    np.save('data/processed/test/prices.npy', price_data)
    np.save('data/processed/test/features.npy', feature_data)
    
    try:
        # Create environment
        env = CryptoTradingEnv(env_config)
        
        # Test reset
        obs, info = env.reset()
        print(f"Observation shape: {obs.shape}")
        print(f"Expected shape: {env.observation_space.shape}")
        
        assert obs.shape == env.observation_space.shape, f"Observation shape mismatch: {obs.shape} vs {env.observation_space.shape}"
        
        # Test step
        action = env.action_space.sample()
        next_obs, reward, terminated, truncated, info = env.step(action)
        
        print(f"Step successful - reward: {reward:.4f}")
        print(f"Next observation shape: {next_obs.shape}")
        
        print("✓ Environment integration working correctly!")
        return env
        
    finally:
        # Clean up
        import shutil
        if os.path.exists('data/processed/test'):
            shutil.rmtree('data/processed/test')

def test_model_environment_integration():
    """Test model with environment observations."""
    print("\nTesting model-environment integration...")
    
    # Create environment config
    env_config = {
        'initial_balance': 10000.0,
        'lookback_window': 168,
        'episode_length': 100,
        'dataset_name': 'test',
        'data_dir': 'data/processed',
        'train_start': [100],
        'train_end': [500],
        'test_start': [500],
        'test_end': [600],
        'regime': 'training'
    }
    
    # Create mock data
    n_samples = 1000
    feature_dim = 96
    price_data = np.random.randn(n_samples, 4)
    feature_data = np.random.randn(n_samples, feature_dim)
    
    # Save mock data
    import os
    os.makedirs('data/processed/test', exist_ok=True)
    np.save('data/processed/test/prices.npy', price_data)
    np.save('data/processed/test/features.npy', feature_data)
    
    try:
        # Create environment
        env = CryptoTradingEnv(env_config)
        obs, info = env.reset()
        
        # Create model config matching environment
        d_obs = 2 + 2 + feature_dim  # time + account + technical
        model_config = TransformerConfig(
            num_obs_in_history=168,
            d_obs=d_obs,
            d_time=2,
            d_account=2,
            d_technical_indicators=feature_dim,
            d_obs_enc=256,
            num_heads=4,
            num_attn_blocks=3,
            output_size=env.action_space.n
        )
        
        # Create model
        model = NewTransformerModel(model_config)
        
        # Test forward pass with environment observation
        obs_tensor = torch.FloatTensor(obs).unsqueeze(0)  # Add batch dimension
        if torch.cuda.is_available():
            obs_tensor = obs_tensor.cuda()
        
        with torch.no_grad():
            policy_logits, value = model(obs_tensor)
        
        print(f"Policy logits shape: {policy_logits.shape}")
        print(f"Value shape: {value.shape}")
        print(f"Action probabilities: {torch.softmax(policy_logits, dim=-1)}")
        print(f"Value estimate: {value.item():.4f}")
        
        # Test action selection
        action_probs = torch.softmax(policy_logits, dim=-1)
        action = torch.multinomial(action_probs, 1).item()
        print(f"Selected action: {action}")
        
        # Test step with selected action
        next_obs, reward, terminated, truncated, info = env.step(action)
        print(f"Step reward: {reward:.4f}")
        
        print("✓ Model-environment integration working correctly!")
        
    finally:
        # Clean up
        import shutil
        if os.path.exists('data/processed/test'):
            shutil.rmtree('data/processed/test')

def main():
    """Main test function."""
    print("=" * 60)
    print("Testing Transformer Integration")
    print("=" * 60)
    
    try:
        # Test segmented scaler
        scaler = test_segmented_scaler()
        
        # Test environment integration
        env = test_environment_integration()
        
        # Test model-environment integration
        test_model_environment_integration()
        
        print("\n" + "=" * 60)
        print("✓ All integration tests passed successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Integration test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
