[project]
name = "rl-ct"
version = "0.1.0"
description = "Reinforcement Learning for Cryptocurrency Trading - A PyTorch and Ray based framework"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "gymnasium[all]>=1.0.0",
    "pandas>=2.0.3",
    "numpy>=1.24.0",
    "ray[data,serve,train,tune]>=2.36.1",
    "tensorboard>=2.14.0",
    "torch>=2.5.1",
    "torchvision>=0.20.1",
    "scikit-learn>=1.3.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.15.0",
    "yfinance>=0.2.18",
    "ccxt>=4.0.0",
    "ta>=0.10.2",
    "pyyaml>=6.0",
    "hydra-core>=1.3.0",
    "wandb>=0.15.0",
    "rich>=13.0.0",
    "typer>=0.9.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "dm-tree>=0.1.9",
    "lz4>=4.4.4",
    "gputil>=1.4.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.3.0",
]

[project.scripts]
rl-ct = "rl_ct.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
