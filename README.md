# RL-CT: Reinforcement Learning for Cryptocurrency Trading

A comprehensive PyTorch and Ray-based framework for training and evaluating reinforcement learning agents on cryptocurrency trading tasks. This framework is inspired by and builds upon the concepts from [deep-reinforcement-learning-for-crypto-trading](https://github.com/xkaple00/deep-reinforcement-learning-for-crypto-trading), providing a modern, scalable, and production-ready implementation.

## 🚀 Features

### Core Capabilities
- **🧠 Advanced Neural Networks**: Transformer-based models with multi-head attention mechanisms
- **⚡ Distributed Training**: Ray RLlib integration for scalable, distributed training
- **🏪 Realistic Trading Environment**: Comprehensive cryptocurrency trading simulation with leverage, fees, and slippage
- **📊 Multi-source Data Support**: Price data, technical indicators, time features, and custom indicators
- **📈 Comprehensive Evaluation**: Backtesting, performance metrics, and rich visualizations
- **⚙️ Easy Configuration**: YAML-based configuration management for all components
- **🖥️ CLI Interface**: Complete command-line tools for all operations

### Technical Features
- **Multiple Model Architectures**: Transformer, MLP, LSTM support
- **Advanced Data Processing**: Automated feature engineering with 50+ technical indicators
- **Risk Management**: Position sizing, stop-loss, take-profit, margin requirements
- **Performance Analytics**: 20+ trading metrics including Sharpe ratio, max drawdown, win rate
- **Visualization Tools**: Interactive plots, equity curves, drawdown analysis
- **Hyperparameter Tuning**: Automated optimization with Ray Tune

## 🏗️ Architecture

```
rl_ct/
├── envs/                    # Trading environments
│   ├── base_env.py         # Base trading environment
│   └── crypto_trading_env.py # Cryptocurrency-specific environment
├── models/                  # Neural network models
│   ├── base_model.py       # Base model interface
│   ├── transformer.py      # Transformer implementation
│   ├── mlp.py             # Multi-layer perceptron models
│   └── rllib_models.py    # RLlib model adapters
├── agents/                  # RL agents
│   ├── base_agent.py       # Base agent interface
│   └── ppo_agent.py       # PPO agent with Ray RLlib
├── utils/                   # Utility functions
│   ├── data_loaders/       # Data loading utilities
│   ├── preprocessing/      # Data preprocessing and feature engineering
│   ├── visualization/      # Plotting and visualization tools
│   ├── config.py          # Configuration management
│   ├── logger.py          # Logging utilities
│   └── metrics.py         # Trading performance metrics
├── scripts/                 # Training and evaluation scripts
│   ├── train.py           # Main training script
│   ├── evaluate.py        # Model evaluation
│   ├── backtest.py        # Backtesting engine
│   └── collect_data.py    # Data collection
├── configs/                 # Configuration files
│   ├── training/          # Training configurations
│   ├── environment/       # Environment settings
│   └── model/            # Model architectures
├── data/                   # Data storage
│   ├── raw/              # Raw market data
│   └── processed/        # Processed training data
├── tests/                  # Comprehensive test suite
├── examples/               # Example scripts and tutorials
└── docs/                   # Documentation
```

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/rl_ct.git
cd rl_ct

# Install dependencies
pip install -e .

# For development
pip install -e ".[dev]"
```

### Run the Demo

```bash
# Run the complete quick start demo
python examples/quick_start.py
```

This will automatically:
1. Generate sample cryptocurrency data
2. Process data with technical indicators
3. Train a small Transformer model
4. Evaluate the model performance
5. Run backtesting analysis
6. Generate visualizations

### Manual Usage

```bash
# 1. Collect real market data
rl-ct collect-data --symbols BTC/USDT,ETH/USDT --days 30 --timeframe 1h

# 2. Train a model
rl-ct train --config configs/training/default.yaml --name my_experiment

# 3. Evaluate the trained model
rl-ct evaluate checkpoints/my_experiment/best_model.zip --episodes 20

# 4. Run backtesting
rl-ct backtest checkpoints/my_experiment/best_model.zip --balance 10000

# 5. Get framework information
rl-ct info
```

## 📊 Key Components

### Trading Environment
- **Realistic Market Simulation**: Includes transaction costs, slippage, and leverage
- **Multiple Order Types**: Market orders, position management, risk controls
- **Flexible Reward Functions**: PnL-based, risk-adjusted (Sharpe, Sortino), custom rewards
- **Comprehensive State Space**: Price data, technical indicators, account information

### Model Architectures
- **Transformer Model**: Multi-head attention for sequence modeling
- **MLP Models**: Standard and residual architectures
- **LSTM Support**: For sequential pattern recognition
- **Custom Models**: Easy extension for new architectures

### Data Processing Pipeline
- **Automated Feature Engineering**: 50+ technical indicators (RSI, MACD, Bollinger Bands, etc.)
- **Time-based Features**: Hour, day of week, market sessions
- **Lag Features**: Historical price and volume patterns
- **Robust Scaling**: Quantile, min-max, and standard scaling options

### Performance Analytics
- **Trading Metrics**: Win rate, profit factor, average trade duration
- **Risk Metrics**: Maximum drawdown, volatility, VaR
- **Risk-adjusted Returns**: Sharpe ratio, Sortino ratio, Calmar ratio
- **Visualization**: Equity curves, drawdown analysis, return distributions

## 🔧 Configuration

### Training Configuration Example

```yaml
# configs/training/my_config.yaml
experiment_name: "btc_trading_transformer"
seed: 42

env:
  initial_balance: 10000.0
  transaction_cost: 0.001
  leverage: 1.0
  episode_length: 168  # 1 week

algorithm:
  name: "PPO"
  learning_rate: 3e-4
  batch_size: 64
  n_epochs: 10
  gamma: 0.99

model:
  type: "transformer"
  d_model: 256
  num_heads: 8
  num_layers: 3
  dropout: 0.1

training:
  total_timesteps: 1000000
  eval_freq: 10000
  save_freq: 50000
```

### Environment Configuration Example

```yaml
# configs/environment/trading.yaml
initial_balance: 10000.0
leverage: 1.0
transaction_cost: 0.001
lookback_window: 168
episode_length: 168

dataset_name: "btc_eth_dataset"
reward_type: "pnl"
reward_scaling: 1.0

scaler:
  type: "quantile"
  min_quantile: 0.5
  max_quantile: 99.5
```

## 📈 Performance Metrics

The framework provides comprehensive performance analysis:

- **Return Metrics**: Total return, annualized return, excess return
- **Risk Metrics**: Volatility, maximum drawdown, downside deviation
- **Risk-adjusted Metrics**: Sharpe ratio, Sortino ratio, Calmar ratio
- **Trading Metrics**: Win rate, profit factor, average trade duration
- **Advanced Metrics**: Skewness, kurtosis, tail ratio

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/test_environment.py
pytest tests/test_models.py
pytest tests/test_integration.py

# Run with coverage
pytest --cov=rl_ct --cov-report=html
```

## 📚 Documentation

- **[User Guide](docs/USER_GUIDE.md)**: Comprehensive usage guide
- **[API Documentation](docs/API.md)**: Detailed API reference
- **[Examples](examples/)**: Example scripts and tutorials
- **[Configuration Guide](docs/CONFIGURATION.md)**: Configuration options

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass (`pytest`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by [deep-reinforcement-learning-for-crypto-trading](https://github.com/xkaple00/deep-reinforcement-learning-for-crypto-trading)
- Built with [Ray RLlib](https://docs.ray.io/en/latest/rllib/index.html) for distributed training
- Uses [PyTorch](https://pytorch.org/) for neural network implementation
- Market data from various cryptocurrency exchanges via [CCXT](https://github.com/ccxt/ccxt)

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/your-username/rl_ct/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/rl_ct/discussions)
- **Documentation**: [User Guide](docs/USER_GUIDE.md)

---

**⚠️ Disclaimer**: This framework is for research and educational purposes. Cryptocurrency trading involves substantial risk of loss. Past performance does not guarantee future results. Always do your own research and consider your risk tolerance before trading.